"""
数据加载和预处理模块
Data Loading and Preprocessing Module

功能：
1. 加载HDF5格式的股票数据
2. 处理中证800权重数据
3. 处理行业分类数据
4. 数据清洗和格式化
"""

import pandas as pd
import numpy as np
import h5py
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class DataLoader:
    """数据加载器类"""

    def __init__(self, data_path=r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data'):
        """
        初始化数据加载器

        Parameters:
        -----------
        data_path : str
            数据文件路径
        """
        self.data_path = data_path
        self.stock_data = None
        self.index_weights = None
        self.industry_data = None
        
    def load_hdf5_data(self, filename='merged_data818.h5'):
        """
        加载HDF5格式的股票数据
        
        Parameters:
        -----------
        filename : str
            HDF5文件名
            
        Returns:
        --------
        pd.DataFrame
            股票数据
        """
        file_path = f"{self.data_path}\\{filename}"
        print(f"正在加载HDF5文件: {file_path}")
        
        try:
            # 方法1: 使用pandas直接读取
            try:
                data = pd.read_hdf(file_path, key='data')
                print(f"成功加载数据，形状: {data.shape}")
                return data
            except Exception as e1:
                print(f"pandas读取失败: {e1}")
                
                # 方法2: 使用h5py读取
                print("尝试使用h5py读取...")
                with h5py.File(file_path, 'r') as f:
                    # 查看文件结构
                    print("HDF5文件结构:")
                    self._print_hdf5_structure(f)
                    
                    # 尝试重构DataFrame
                    data = self._reconstruct_dataframe_from_hdf5(f)
                    return data
                    
        except Exception as e:
            print(f"加载HDF5文件失败: {e}")
            return None
    
    def _print_hdf5_structure(self, h5_file, prefix=""):
        """打印HDF5文件结构"""
        for key in h5_file.keys():
            item = h5_file[key]
            print(f"{prefix}{key}: {type(item).__name__}")
            if hasattr(item, 'shape'):
                print(f"{prefix}  Shape: {item.shape}")
            if hasattr(item, 'dtype'):
                print(f"{prefix}  Dtype: {item.dtype}")
            if isinstance(item, h5py.Group):
                self._print_hdf5_structure(item, prefix + "  ")
    
    def _reconstruct_dataframe_from_hdf5(self, h5_file):
        """从HDF5文件重构DataFrame"""
        try:
            # 获取数据组
            data_group = h5_file['data']
            
            # 读取索引和列信息
            if 'axis0' in data_group and 'axis1' in data_group:
                # 读取列名
                columns = []
                if 'block0_items' in data_group:
                    block0_items = data_group['block0_items'][:]
                    columns.extend([item.decode('utf-8') if isinstance(item, bytes) else str(item) for item in block0_items])
                if 'block1_items' in data_group:
                    block1_items = data_group['block1_items'][:]
                    columns.extend([item.decode('utf-8') if isinstance(item, bytes) else str(item) for item in block1_items])
                if 'block2_items' in data_group:
                    block2_items = data_group['block2_items'][:]
                    columns.extend([item.decode('utf-8') if isinstance(item, bytes) else str(item) for item in block2_items])
                
                # 读取索引
                index = data_group['axis1'][:]
                
                # 读取数据值
                values_list = []
                if 'block0_values' in data_group:
                    values_list.append(data_group['block0_values'][:])
                if 'block1_values' in data_group:
                    values_list.append(data_group['block1_values'][:])
                
                # 合并数据
                if values_list:
                    values = np.concatenate(values_list, axis=1)
                    
                    # 创建DataFrame
                    df = pd.DataFrame(values, index=index, columns=columns[:values.shape[1]])
                    print(f"成功重构DataFrame，形状: {df.shape}")
                    return df
            
            print("无法重构DataFrame")
            return None
            
        except Exception as e:
            print(f"重构DataFrame失败: {e}")
            return None
    
    def load_index_weights(self, filename='CSI800stocks.csv'):
        """
        加载中证800权重数据
        
        Parameters:
        -----------
        filename : str
            权重文件名
            
        Returns:
        --------
        pd.DataFrame
            权重数据
        """
        file_path = f"{self.data_path}\\{filename}"
        print(f"正在加载权重数据: {file_path}")
        
        try:
            data = pd.read_csv(file_path)
            
            # 数据预处理
            data['trade_date'] = pd.to_datetime(data['trade_date'])
            data = data.sort_values(['trade_date', 'ts_code'])
            
            print(f"权重数据加载成功，形状: {data.shape}")
            print(f"日期范围: {data['trade_date'].min()} 到 {data['trade_date'].max()}")
            print(f"股票数量: {data['ts_code'].nunique()}")
            
            return data
            
        except Exception as e:
            print(f"加载权重数据失败: {e}")
            return None
    
    def load_industry_data(self, filename='swind.xlsx'):
        """
        加载行业分类数据
        
        Parameters:
        -----------
        filename : str
            行业文件名
            
        Returns:
        --------
        pd.DataFrame
            行业数据
        """
        file_path = f"{self.data_path}\\{filename}"
        print(f"正在加载行业数据: {file_path}")
        
        try:
            data = pd.read_excel(file_path)
            
            # 数据预处理
            # 处理日期列
            if 'in_date' in data.columns:
                data['in_date'] = pd.to_datetime(data['in_date'], format='%Y%m%d', errors='coerce')
            if 'out_date' in data.columns:
                data['out_date'] = pd.to_datetime(data['out_date'], format='%Y%m%d', errors='coerce')
            
            # 选择最新的行业分类（每个股票保留最新的记录）
            data = data.sort_values('in_date').groupby('ts_code').last().reset_index()
            
            print(f"行业数据加载成功，形状: {data.shape}")
            print(f"股票数量: {data['ts_code'].nunique()}")
            
            # 显示行业分布
            if 'l1_name' in data.columns:
                industry_counts = data['l1_name'].value_counts()
                print(f"一级行业分布:\n{industry_counts.head(10)}")
            
            return data
            
        except Exception as e:
            print(f"加载行业数据失败: {e}")
            return None
    
    def load_all_data(self):
        """加载所有数据"""
        print("=== 开始加载所有数据 ===\n")
        
        # 加载股票数据
        self.stock_data = self.load_hdf5_data()
        
        # 加载权重数据
        self.index_weights = self.load_index_weights()
        
        # 加载行业数据
        self.industry_data = self.load_industry_data()
        
        print("\n=== 数据加载完成 ===")
        
        return {
            'stock_data': self.stock_data,
            'index_weights': self.index_weights,
            'industry_data': self.industry_data
        }
    
    def get_data_summary(self):
        """获取数据摘要信息"""
        summary = {}
        
        if self.stock_data is not None:
            summary['stock_data'] = {
                'shape': self.stock_data.shape,
                'columns': list(self.stock_data.columns),
                'date_range': (self.stock_data.index.min(), self.stock_data.index.max()) if hasattr(self.stock_data.index, 'min') else None
            }
        
        if self.index_weights is not None:
            summary['index_weights'] = {
                'shape': self.index_weights.shape,
                'date_range': (self.index_weights['trade_date'].min(), self.index_weights['trade_date'].max()),
                'unique_stocks': self.index_weights['ts_code'].nunique(),
                'unique_dates': self.index_weights['trade_date'].nunique()
            }
        
        if self.industry_data is not None:
            summary['industry_data'] = {
                'shape': self.industry_data.shape,
                'unique_stocks': self.industry_data['ts_code'].nunique(),
                'industries': self.industry_data['l1_name'].nunique() if 'l1_name' in self.industry_data.columns else None
            }
        
        return summary
    
    def validate_data_consistency(self):
        """验证数据一致性"""
        print("=== 验证数据一致性 ===")
        
        issues = []
        
        if self.index_weights is not None and self.industry_data is not None:
            # 检查股票代码一致性
            weight_stocks = set(self.index_weights['ts_code'].unique())
            industry_stocks = set(self.industry_data['ts_code'].unique())
            
            missing_in_industry = weight_stocks - industry_stocks
            missing_in_weights = industry_stocks - weight_stocks
            
            if missing_in_industry:
                issues.append(f"权重数据中有{len(missing_in_industry)}只股票在行业数据中缺失")
            
            if missing_in_weights:
                issues.append(f"行业数据中有{len(missing_in_weights)}只股票在权重数据中缺失")
            
            overlap = len(weight_stocks & industry_stocks)
            print(f"权重数据和行业数据的股票重叠数量: {overlap}")
        
        if issues:
            print("发现的问题:")
            for issue in issues:
                print(f"- {issue}")
        else:
            print("数据一致性检查通过")
        
        return issues


def main():
    """测试数据加载功能"""
    print("=== 数据加载测试 ===\n")
    
    # 创建数据加载器
    loader = DataLoader()
    
    # 加载所有数据
    data = loader.load_all_data()
    
    # 获取数据摘要
    summary = loader.get_data_summary()
    print("\n=== 数据摘要 ===")
    for data_type, info in summary.items():
        print(f"\n{data_type}:")
        for key, value in info.items():
            print(f"  {key}: {value}")
    
    # 验证数据一致性
    loader.validate_data_consistency()


if __name__ == "__main__":
    main()
