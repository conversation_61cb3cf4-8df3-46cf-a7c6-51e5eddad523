"""
指数中性可视化分析
用图表直观展示指数中性和行业中性的效果
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

def load_and_prepare_data():
    """加载和准备数据"""
    # 加载数据
    weights_df = pd.read_csv(r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\CSI800stocks.csv")
    industry_df = pd.read_excel(r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\swind.xlsx")
    
    # 数据预处理
    weights_df['trade_date'] = pd.to_datetime(weights_df['trade_date'])
    latest_date = weights_df['trade_date'].max()
    latest_weights = weights_df[weights_df['trade_date'] == latest_date].copy()
    
    # 行业数据去重
    industry_df = industry_df.sort_values('in_date').groupby('ts_code').last().reset_index()
    
    # 合并数据
    merged_df = latest_weights.merge(industry_df[['ts_code', 'l1_name']], on='ts_code', how='left')
    
    return merged_df, latest_date

def plot_index_composition(merged_df):
    """绘制指数构成分析"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 权重分布直方图
    axes[0, 0].hist(merged_df['weight'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0, 0].set_title('中证800权重分布', fontsize=14, fontweight='bold')
    axes[0, 0].set_xlabel('权重 (%)')
    axes[0, 0].set_ylabel('股票数量')
    axes[0, 0].axvline(merged_df['weight'].mean(), color='red', linestyle='--', 
                       label=f'平均权重: {merged_df["weight"].mean():.3f}%')
    axes[0, 0].legend()
    
    # 2. 前20大权重股
    top20 = merged_df.nlargest(20, 'weight')
    axes[0, 1].barh(range(len(top20)), top20['weight'], color='lightcoral')
    axes[0, 1].set_yticks(range(len(top20)))
    axes[0, 1].set_yticklabels(top20['ts_code'], fontsize=8)
    axes[0, 1].set_title('前20大权重股', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('权重 (%)')
    
    # 3. 行业权重分布
    industry_weights = merged_df.groupby('l1_name')['weight'].sum().sort_values(ascending=False)
    top_industries = industry_weights.head(12)
    
    axes[1, 0].bar(range(len(top_industries)), top_industries.values, color='lightgreen')
    axes[1, 0].set_xticks(range(len(top_industries)))
    axes[1, 0].set_xticklabels(top_industries.index, rotation=45, ha='right', fontsize=9)
    axes[1, 0].set_title('前12大行业权重', fontsize=14, fontweight='bold')
    axes[1, 0].set_ylabel('权重 (%)')
    
    # 4. 行业权重饼图
    colors = plt.cm.Set3(np.linspace(0, 1, len(top_industries)))
    axes[1, 1].pie(top_industries.values, labels=top_industries.index, autopct='%1.1f%%',
                   colors=colors, textprops={'fontsize': 8})
    axes[1, 1].set_title('行业权重占比', fontsize=14, fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    return industry_weights

def demonstrate_neutralization_effect():
    """演示中性化效果"""
    # 模拟数据：假设市场和行业收益率
    dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
    
    # 模拟市场收益率
    market_returns = np.random.normal(0.0005, 0.015, len(dates))  # 年化约12%收益，15%波动
    market_cumulative = (1 + pd.Series(market_returns, index=dates)).cumprod()
    
    # 模拟选股Alpha
    alpha_returns = np.random.normal(0.0008, 0.01, len(dates))  # 年化约20%收益，10%波动
    alpha_cumulative = (1 + pd.Series(alpha_returns, index=dates)).cumprod()
    
    # 普通多头策略 = 市场收益 + Alpha
    long_only_returns = market_returns + alpha_returns
    long_only_cumulative = (1 + pd.Series(long_only_returns, index=dates)).cumprod()
    
    # 指数中性策略 ≈ Alpha（消除市场影响）
    neutral_returns = alpha_returns + np.random.normal(0, 0.002, len(dates))  # 加入少量跟踪误差
    neutral_cumulative = (1 + pd.Series(neutral_returns, index=dates)).cumprod()
    
    # 绘图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 累计收益率对比
    axes[0, 0].plot(dates, market_cumulative, label='市场指数', linewidth=2)
    axes[0, 0].plot(dates, long_only_cumulative, label='普通多头策略', linewidth=2)
    axes[0, 0].plot(dates, neutral_cumulative, label='指数中性策略', linewidth=2)
    axes[0, 0].set_title('策略收益率对比', fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('累计收益率')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 收益率分布
    axes[0, 1].hist(market_returns, bins=30, alpha=0.5, label='市场收益率', density=True)
    axes[0, 1].hist(long_only_returns, bins=30, alpha=0.5, label='多头策略', density=True)
    axes[0, 1].hist(neutral_returns, bins=30, alpha=0.5, label='中性策略', density=True)
    axes[0, 1].set_title('日收益率分布', fontsize=14, fontweight='bold')
    axes[0, 1].set_xlabel('日收益率')
    axes[0, 1].legend()
    
    # 3. 风险指标对比
    strategies = ['市场指数', '普通多头', '指数中性']
    returns_data = [market_returns, long_only_returns, neutral_returns]
    
    annual_returns = [np.mean(r) * 252 for r in returns_data]
    annual_vols = [np.std(r) * np.sqrt(252) for r in returns_data]
    sharpe_ratios = [ar/av for ar, av in zip(annual_returns, annual_vols)]
    
    x = np.arange(len(strategies))
    width = 0.25
    
    axes[1, 0].bar(x - width, annual_returns, width, label='年化收益率', alpha=0.8)
    axes[1, 0].bar(x, annual_vols, width, label='年化波动率', alpha=0.8)
    axes[1, 0].bar(x + width, sharpe_ratios, width, label='夏普比率', alpha=0.8)
    
    axes[1, 0].set_title('风险收益指标对比', fontsize=14, fontweight='bold')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(strategies)
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 相关性分析
    correlation_matrix = np.corrcoef([market_returns, long_only_returns, neutral_returns])
    
    im = axes[1, 1].imshow(correlation_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    axes[1, 1].set_xticks(range(len(strategies)))
    axes[1, 1].set_yticks(range(len(strategies)))
    axes[1, 1].set_xticklabels(strategies)
    axes[1, 1].set_yticklabels(strategies)
    axes[1, 1].set_title('策略相关性矩阵', fontsize=14, fontweight='bold')
    
    # 添加相关系数标注
    for i in range(len(strategies)):
        for j in range(len(strategies)):
            axes[1, 1].text(j, i, f'{correlation_matrix[i, j]:.2f}',
                           ha="center", va="center", color="black", fontweight='bold')
    
    plt.colorbar(im, ax=axes[1, 1])
    plt.tight_layout()
    plt.show()
    
    return {
        'annual_returns': annual_returns,
        'annual_vols': annual_vols,
        'sharpe_ratios': sharpe_ratios,
        'correlations': correlation_matrix
    }

def create_portfolio_example(merged_df):
    """创建组合示例"""
    # 选择5只股票
    selected_stocks = ['601318.SH', '600519.SH', '300750.SZ', '600036.SH', '601166.SH']
    
    # 获取这些股票的信息
    selected_info = merged_df[merged_df['ts_code'].isin(selected_stocks)].copy()
    
    # 计算指数中性权重
    long_weight = 0.2  # 每只股票20%
    portfolio_data = []
    
    for _, stock in selected_info.iterrows():
        # 多头权重
        portfolio_data.append({
            'stock': stock['ts_code'],
            'industry': stock['l1_name'],
            'index_weight': stock['weight'],
            'long_position': long_weight,
            'short_position': -stock['weight']/100,  # 按指数权重做空
            'net_position': long_weight - stock['weight']/100
        })
    
    portfolio_df = pd.DataFrame(portfolio_data)
    
    # 可视化
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. 多头vs空头权重
    x = range(len(portfolio_df))
    axes[0, 0].bar([i-0.2 for i in x], portfolio_df['long_position'], 0.4, 
                   label='多头权重', color='green', alpha=0.7)
    axes[0, 0].bar([i+0.2 for i in x], portfolio_df['short_position'], 0.4, 
                   label='空头权重', color='red', alpha=0.7)
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(portfolio_df['stock'], rotation=45)
    axes[0, 0].set_title('多空头寸对比', fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('权重')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 净头寸
    colors = ['green' if x > 0 else 'red' for x in portfolio_df['net_position']]
    axes[0, 1].bar(x, portfolio_df['net_position'], color=colors, alpha=0.7)
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels(portfolio_df['stock'], rotation=45)
    axes[0, 1].set_title('净头寸（多头-空头）', fontsize=14, fontweight='bold')
    axes[0, 1].set_ylabel('净权重')
    axes[0, 1].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. 行业暴露
    industry_exposure = portfolio_df.groupby('industry')['net_position'].sum()
    axes[1, 0].bar(range(len(industry_exposure)), industry_exposure.values, 
                   color='orange', alpha=0.7)
    axes[1, 0].set_xticks(range(len(industry_exposure)))
    axes[1, 0].set_xticklabels(industry_exposure.index, rotation=45, ha='right')
    axes[1, 0].set_title('行业净暴露', fontsize=14, fontweight='bold')
    axes[1, 0].set_ylabel('净权重')
    axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 组合统计
    total_long = portfolio_df['long_position'].sum()
    total_short = portfolio_df['short_position'].sum()
    net_exposure = portfolio_df['net_position'].sum()
    
    stats_data = {
        '多头总权重': total_long,
        '空头总权重': abs(total_short),
        '净市场暴露': net_exposure,
        '多空比例': total_long / abs(total_short) if total_short != 0 else 0
    }
    
    axes[1, 1].bar(range(len(stats_data)), list(stats_data.values()), 
                   color=['green', 'red', 'blue', 'purple'], alpha=0.7)
    axes[1, 1].set_xticks(range(len(stats_data)))
    axes[1, 1].set_xticklabels(list(stats_data.keys()), rotation=45, ha='right')
    axes[1, 1].set_title('组合统计指标', fontsize=14, fontweight='bold')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 添加数值标注
    for i, v in enumerate(stats_data.values()):
        axes[1, 1].text(i, v + 0.01, f'{v:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    return portfolio_df, stats_data

def main():
    """主函数"""
    print("指数中性可视化分析")
    print("="*50)
    
    # 1. 加载数据
    print("正在加载数据...")
    merged_df, latest_date = load_and_prepare_data()
    print(f"数据日期: {latest_date.strftime('%Y-%m-%d')}")
    print(f"成分股数量: {len(merged_df)}")
    
    # 2. 指数构成分析
    print("\n正在生成指数构成分析图表...")
    industry_weights = plot_index_composition(merged_df)
    
    # 3. 中性化效果演示
    print("\n正在演示中性化效果...")
    performance_stats = demonstrate_neutralization_effect()
    
    # 4. 组合示例
    print("\n正在创建组合示例...")
    portfolio_df, stats_data = create_portfolio_example(merged_df)
    
    # 5. 输出关键结论
    print("\n" + "="*50)
    print("关键结论:")
    print("="*50)
    print(f"1. 中证800指数包含{len(merged_df)}只股票")
    print(f"2. 前10大行业权重占比: {industry_weights.head(10).sum():.1f}%")
    print(f"3. 指数中性策略净市场暴露: {stats_data['净市场暴露']:.6f}")
    print(f"4. 多空比例: {stats_data['多空比例']:.3f}")
    print("\n指数中性的核心价值:")
    print("- 消除市场系统性风险")
    print("- 专注于个股选择能力")
    print("- 在震荡市场中获得稳定收益")

if __name__ == "__main__":
    main()
