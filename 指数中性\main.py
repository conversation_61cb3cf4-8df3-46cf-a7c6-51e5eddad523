"""
指数中性策略主执行脚本
Main Execution Script for Index Neutral Strategy

整合所有功能模块，提供完整的指数中性策略分析流程
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入自定义模块
from data_loader import DataLoader
from neutral_calculator import NeutralCalculator
from config import get_data_path, STRATEGY_CONFIG

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class IndexNeutralMain:
    """指数中性策略主类"""

    def __init__(self, data_path=None):
        """
        初始化

        Parameters:
        -----------
        data_path : str, optional
            数据文件路径，如果为None则使用配置文件中的路径
        """
        if data_path is None:
            data_path = get_data_path()
        self.data_path = data_path
        self.loader = DataLoader(data_path)
        self.calculator = None
        self.data = {}
        
    def run_analysis(self, long_stocks=None, method='weight_based'):
        """
        运行完整的指数中性分析
        
        Parameters:
        -----------
        long_stocks : list, optional
            多头股票列表，如果为None则使用默认股票
        method : str
            中性化方法
        """
        print("=" * 60)
        print("指数中性策略分析")
        print("=" * 60)
        
        # 1. 加载数据
        print("\n1. 数据加载阶段")
        print("-" * 30)
        if not self._load_data():
            print("数据加载失败，程序退出")
            return False
        
        # 2. 数据验证
        print("\n2. 数据验证阶段")
        print("-" * 30)
        self._validate_data()
        
        # 3. 策略配置
        print("\n3. 策略配置阶段")
        print("-" * 30)
        if long_stocks is None:
            long_stocks = self._get_default_long_stocks()
        
        print(f"多头股票池: {long_stocks}")
        print(f"中性化方法: {method}")
        
        # 4. 计算中性权重
        print("\n4. 权重计算阶段")
        print("-" * 30)
        neutral_weights = self._calculate_weights(long_stocks, method)
        
        if not neutral_weights:
            print("权重计算失败")
            return False
        
        # 5. 回测分析
        print("\n5. 回测分析阶段")
        print("-" * 30)
        backtest_results = self._run_backtest(neutral_weights)
        
        # 6. 结果分析
        print("\n6. 结果分析阶段")
        print("-" * 30)
        self._analyze_results(backtest_results)
        
        # 7. 可视化
        print("\n7. 结果可视化")
        print("-" * 30)
        self._create_visualizations(backtest_results, neutral_weights)
        
        print("\n" + "=" * 60)
        print("分析完成！")
        print("=" * 60)
        
        return True
    
    def _load_data(self):
        """加载数据"""
        try:
            # 加载所有数据
            self.data = self.loader.load_all_data()
            
            # 检查关键数据是否加载成功
            if self.data['index_weights'] is None:
                print("错误：权重数据加载失败")
                return False
            
            if self.data['industry_data'] is None:
                print("错误：行业数据加载失败")
                return False
            
            # 创建计算器
            self.calculator = NeutralCalculator(
                self.data['index_weights'],
                self.data['industry_data'],
                self.data['stock_data']
            )
            
            return True
            
        except Exception as e:
            print(f"数据加载异常: {e}")
            return False
    
    def _validate_data(self):
        """验证数据"""
        # 获取数据摘要
        summary = self.loader.get_data_summary()
        
        print("数据摘要:")
        for data_type, info in summary.items():
            print(f"\n{data_type}:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        
        # 验证数据一致性
        issues = self.loader.validate_data_consistency()
        
        if issues:
            print(f"\n发现 {len(issues)} 个数据问题，但继续执行分析")
        else:
            print("\n数据验证通过")
    
    def _get_default_long_stocks(self):
        """获取默认的多头股票池"""
        # 优先使用配置文件中的股票池
        if STRATEGY_CONFIG['default_long_stocks']:
            return STRATEGY_CONFIG['default_long_stocks']

        # 从权重数据中选择权重较大的股票作为备选
        if self.data['index_weights'] is not None:
            # 计算平均权重，选择前5只股票
            avg_weights = self.data['index_weights'].groupby('ts_code')['weight'].mean()
            top_stocks = avg_weights.nlargest(5).index.tolist()
            return top_stocks
        else:
            # 最后的默认股票池
            return ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    def _calculate_weights(self, long_stocks, method):
        """计算中性权重"""
        try:
            neutral_weights = self.calculator.calculate_neutral_weights(
                long_stocks, method=method
            )
            
            print(f"成功计算 {len(neutral_weights)} 个调仓期的权重")
            
            # 显示第一个调仓期的权重示例
            if neutral_weights:
                first_date = min(neutral_weights.keys())
                first_weights = neutral_weights[first_date]
                
                print(f"\n{first_date.strftime('%Y-%m-%d')} 权重示例:")
                # 显示权重最大的前10个和最小的前10个
                sorted_weights = sorted(first_weights.items(), key=lambda x: x[1], reverse=True)
                
                print("多头前5:")
                for stock, weight in sorted_weights[:5]:
                    if weight > 0:
                        print(f"  {stock}: {weight:.4f}")
                
                print("空头前5:")
                for stock, weight in sorted_weights[-5:]:
                    if weight < 0:
                        print(f"  {stock}: {weight:.4f}")
            
            return neutral_weights
            
        except Exception as e:
            print(f"权重计算失败: {e}")
            return None
    
    def _run_backtest(self, neutral_weights):
        """运行回测"""
        try:
            backtest_results = self.calculator.backtest_strategy(neutral_weights)
            
            if len(backtest_results) > 0:
                print(f"回测完成，共 {len(backtest_results)} 个调仓期")
                print(f"回测期间: {backtest_results['date'].min()} 到 {backtest_results['date'].max()}")
            else:
                print("回测结果为空")
            
            return backtest_results
            
        except Exception as e:
            print(f"回测失败: {e}")
            return pd.DataFrame()
    
    def _analyze_results(self, backtest_results):
        """分析结果"""
        if len(backtest_results) == 0:
            print("没有回测结果可分析")
            return
        
        try:
            # 计算策略表现指标
            metrics = self.calculator.analyze_performance(backtest_results)
            
            print("策略表现指标:")
            print("-" * 40)
            
            for key, value in metrics.items():
                if key in ['总收益率', '年化收益率', '年化波动率', '最大回撤']:
                    print(f"{key:12}: {value:8.2%}")
                elif key in ['胜率']:
                    print(f"{key:12}: {value:8.2%}")
                else:
                    print(f"{key:12}: {value:8.3f}")
            
            # 风险分析
            print("\n风险分析:")
            print("-" * 40)
            
            market_exposure = backtest_results['market_exposure']
            print(f"平均市场暴露: {market_exposure.mean():8.3f}")
            print(f"市场暴露标准差: {market_exposure.std():8.3f}")
            print(f"市场暴露范围: [{market_exposure.min():.3f}, {market_exposure.max():.3f}]")
            
            if 'long_short_ratio' in backtest_results.columns:
                ls_ratio = backtest_results['long_short_ratio']
                print(f"平均多空比例: {ls_ratio.mean():8.3f}")
            
        except Exception as e:
            print(f"结果分析失败: {e}")
    
    def _create_visualizations(self, backtest_results, neutral_weights):
        """创建可视化图表"""
        if len(backtest_results) == 0:
            print("没有数据可视化")
            return
        
        try:
            # 使用计算器的绘图功能
            self.calculator.plot_results(backtest_results)
            
            # 额外的分析图表
            self._plot_additional_analysis(backtest_results, neutral_weights)
            
        except Exception as e:
            print(f"可视化失败: {e}")
    
    def _plot_additional_analysis(self, backtest_results, neutral_weights):
        """绘制额外的分析图表"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # 权重分布分析
            if neutral_weights:
                all_weights = []
                for weights in neutral_weights.values():
                    all_weights.extend(weights.values())
                
                axes[0, 0].hist(all_weights, bins=50, alpha=0.7)
                axes[0, 0].set_title('权重分布')
                axes[0, 0].set_xlabel('权重')
                axes[0, 0].axvline(x=0, color='r', linestyle='--', alpha=0.5)
            
            # 收益率滚动统计
            if len(backtest_results) >= 12:
                rolling_return = backtest_results['return'].rolling(12).mean()
                rolling_vol = backtest_results['return'].rolling(12).std()
                
                axes[0, 1].plot(backtest_results['date'], rolling_return, label='滚动收益率')
                axes[0, 1].set_title('12期滚动收益率')
                axes[0, 1].legend()
                axes[0, 1].tick_params(axis='x', rotation=45)
            
            # 市场暴露度分析
            axes[1, 0].plot(backtest_results['date'], backtest_results['market_exposure'])
            axes[1, 0].fill_between(backtest_results['date'], 
                                   backtest_results['market_exposure'], 
                                   0, alpha=0.3)
            axes[1, 0].axhline(y=0, color='r', linestyle='--')
            axes[1, 0].set_title('市场暴露度时序')
            axes[1, 0].tick_params(axis='x', rotation=45)
            
            # 收益率vs市场暴露散点图
            axes[1, 1].scatter(backtest_results['market_exposure'], 
                              backtest_results['return'], alpha=0.6)
            axes[1, 1].set_xlabel('市场暴露度')
            axes[1, 1].set_ylabel('期间收益率')
            axes[1, 1].set_title('收益率 vs 市场暴露度')
            
            plt.tight_layout()
            plt.show()
            
        except Exception as e:
            print(f"额外分析图表绘制失败: {e}")


def main():
    """主函数"""
    print("指数中性策略分析系统")
    print("Index Neutral Strategy Analysis System")
    print("=" * 60)
    
    # 创建主分析对象
    analyzer = IndexNeutralMain()
    
    # 配置参数
    long_stocks = None  # 使用默认股票池
    method = 'weight_based'  # 使用权重中性化方法
    
    # 运行分析
    success = analyzer.run_analysis(long_stocks=long_stocks, method=method)
    
    if success:
        print("\n分析成功完成！")
        
        # 可以尝试其他方法
        print("\n尝试行业中性方法...")
        analyzer.run_analysis(long_stocks=long_stocks, method='industry_neutral')
    else:
        print("\n分析失败，请检查数据和配置")


if __name__ == "__main__":
    main()
