"""
完整市场中性策略构建
1. 使用真实行业数据剔除行业因素
2. 基于中证800权重构建市场中性组合
3. 完整的可视化展示和回测分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import h5py
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class MarketNeutralStrategy:
    """完整市场中性策略类"""
    
    def __init__(self):
        self.factor_data = None
        self.industry_data = None
        self.csi800_weights = None
        self.neutralized_factors = None
        self.portfolio_results = None
        
    def load_all_data(self):
        """加载所有必要数据"""
        print("=== 加载所有数据 ===")
        
        # 1. 加载因子数据
        if not self._load_factor_data():
            return False
            
        # 2. 加载行业数据
        if not self._load_industry_data():
            return False
            
        # 3. 加载中证800权重数据
        if not self._load_csi800_weights():
            return False
            
        print("所有数据加载完成！")
        return True
    
    def _load_factor_data(self, max_records=200000):
        """加载因子数据（限制数量以提高速度）"""
        print("加载因子数据...")
        
        factor_path = r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\factor.h5'
        
        try:
            with h5py.File(factor_path, 'r') as f:
                data_group = f['data']
                
                # 读取基本信息
                factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) 
                              for name in data_group['block0_items'][:]]
                
                # 读取部分数据
                factor_values = data_group['block0_values'][:max_records]
                dates_idx = data_group['axis1_label0'][:max_records]
                stocks_idx = data_group['axis1_label1'][:max_records]
                
                # 读取索引映射
                date_levels = data_group['axis1_level0'][:]
                stock_levels = data_group['axis1_level1'][:]
                
                # 解码股票代码
                stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) 
                             for code in stock_levels]
                
                # 构建DataFrame
                factor_data_list = []
                
                for i in range(len(factor_values)):
                    if i % 20000 == 0:
                        print(f"处理进度: {i}/{len(factor_values)}")
                    
                    date_idx = dates_idx[i]
                    stock_idx = stocks_idx[i]
                    
                    if date_idx < len(date_levels) and stock_idx < len(stock_codes):
                        # 处理日期
                        date_raw = date_levels[date_idx]
                        try:
                            if date_raw > 1e15:
                                date = pd.to_datetime(date_raw, unit='ns')
                            elif date_raw > 1e12:
                                date = pd.to_datetime(date_raw, unit='ms')
                            elif date_raw > 1e9:
                                date = pd.to_datetime(date_raw, unit='s')
                            else:
                                date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')
                        except:
                            continue
                        
                        stock = stock_codes[stock_idx]
                        pb_val = factor_values[i, 0]
                        mv_val = factor_values[i, 1]
                        
                        if not (np.isnan(pb_val) or np.isnan(mv_val)) and pb_val > 0 and mv_val > 0:
                            factor_data_list.append({
                                'date': date,
                                'ts_code': stock,
                                'pb': pb_val,
                                'total_mv': mv_val
                            })
                
                self.factor_data = pd.DataFrame(factor_data_list)
                
                if len(self.factor_data) > 0:
                    self.factor_data = self.factor_data.sort_values(['date', 'ts_code'])
                    print(f"成功加载因子数据: {len(self.factor_data)}条记录")
                    print(f"日期范围: {self.factor_data['date'].min()} 到 {self.factor_data['date'].max()}")
                    return True
                else:
                    return False
                    
        except Exception as e:
            print(f"加载因子数据失败: {e}")
            return False
    
    def _load_industry_data(self):
        """加载行业分类数据"""
        print("加载行业分类数据...")
        
        try:
            industry_path = r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\swind.xlsx'
            self.industry_data = pd.read_excel(industry_path)
            
            # 处理行业数据：取每只股票最新的行业分类
            self.industry_data = self.industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()
            
            print(f"成功加载行业数据: {len(self.industry_data)}只股票")
            print(f"行业数量: {self.industry_data['l1_name'].nunique()}")
            print("主要行业分布:")
            print(self.industry_data['l1_name'].value_counts().head())
            
            return True
            
        except Exception as e:
            print(f"加载行业数据失败: {e}")
            return False
    
    def _load_csi800_weights(self):
        """加载中证800权重数据"""
        print("加载中证800权重数据...")
        
        try:
            weights_path = r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\CSI800stocks.csv'
            self.csi800_weights = pd.read_csv(weights_path)
            
            # 处理日期格式
            self.csi800_weights['trade_date'] = pd.to_datetime(self.csi800_weights['trade_date'])
            
            # 只保留月末数据（每月最后一个交易日）
            self.csi800_weights['year_month'] = self.csi800_weights['trade_date'].dt.to_period('M')
            monthly_weights = self.csi800_weights.groupby(['year_month', 'ts_code']).last().reset_index()
            
            self.csi800_weights = monthly_weights
            
            print(f"成功加载中证800权重数据: {len(self.csi800_weights)}条记录")
            print(f"时间范围: {self.csi800_weights['trade_date'].min()} 到 {self.csi800_weights['trade_date'].max()}")
            print(f"平均每月股票数: {self.csi800_weights.groupby('year_month')['ts_code'].count().mean():.0f}")
            
            return True
            
        except Exception as e:
            print(f"加载中证800权重数据失败: {e}")
            return False
    
    def industry_neutralization(self, factor_name='pb'):
        """
        行业中性化处理 - 详细步骤解释
        
        核心思路：
        1. 对每个交易日，将股票按行业分组
        2. 使用线性回归剔除行业系统性影响
        3. 保留个股在行业内的相对优势
        """
        print(f"\n=== {factor_name}因子行业中性化处理 ===")
        
        # 步骤1：合并因子数据和行业数据
        print("步骤1：合并因子数据和行业数据")
        merged_data = self.factor_data.merge(
            self.industry_data[['ts_code', 'l1_name']], 
            on='ts_code', 
            how='left'
        )
        
        # 过滤掉没有行业分类的股票
        merged_data = merged_data.dropna(subset=['l1_name'])
        print(f"合并后数据量: {len(merged_data)}条")
        print(f"涉及行业数: {merged_data['l1_name'].nunique()}")
        
        neutralized_results = []
        
        # 步骤2：按日期分组进行中性化处理
        print("步骤2：按日期进行行业中性化回归")
        
        dates = sorted(merged_data['date'].unique())
        
        for i, date in enumerate(dates):
            if i % 50 == 0:
                print(f"处理进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})")
            
            # 获取当日数据
            date_data = merged_data[merged_data['date'] == date].copy()
            
            if len(date_data) < 20:  # 至少需要20只股票
                continue
            
            # 步骤3：创建行业虚拟变量
            # 为每个行业创建0-1变量
            industry_dummies = pd.get_dummies(date_data['l1_name'], prefix='industry')
            
            # 步骤4：线性回归 - 剔除行业因素
            try:
                X = industry_dummies.values  # 行业虚拟变量矩阵
                y = date_data[factor_name].values  # 因子值向量
                
                # 过滤有效数据
                valid_mask = ~np.isnan(y)
                if valid_mask.sum() < 10:
                    continue
                
                X_valid = X[valid_mask]
                y_valid = y[valid_mask]
                
                # 添加截距项进行回归
                X_with_intercept = np.column_stack([np.ones(len(X_valid)), X_valid])
                
                # 最小二乘法求解: β = (X'X)^(-1)X'y
                beta = np.linalg.lstsq(X_with_intercept, y_valid, rcond=None)[0]
                
                # 计算预测值和残差
                y_pred = X_with_intercept @ beta
                residuals = y_valid - y_pred  # 残差即为中性化后的因子值
                
                # 步骤5：保存中性化结果
                result_data = date_data[valid_mask].copy()
                result_data[f'{factor_name}_neutralized'] = residuals
                result_data[f'{factor_name}_predicted'] = y_pred  # 保存预测值用于分析
                
                neutralized_results.append(result_data)
                
            except Exception as e:
                print(f"日期 {date} 回归失败: {e}")
                continue
        
        # 步骤6：合并所有结果
        if neutralized_results:
            self.neutralized_factors = pd.concat(neutralized_results, ignore_index=True)
            print(f"\n行业中性化完成！")
            print(f"处理了{len(self.neutralized_factors)}条记录")
            print(f"涉及{self.neutralized_factors['ts_code'].nunique()}只股票")
            
            # 验证中性化效果
            self._validate_neutralization(factor_name)
            
            return True
        else:
            print("行业中性化失败")
            return False
    
    def _validate_neutralization(self, factor_name):
        """验证行业中性化效果"""
        print(f"\n=== 验证{factor_name}中性化效果 ===")
        
        # 选择一个日期进行验证
        sample_date = self.neutralized_factors['date'].iloc[len(self.neutralized_factors)//2]
        sample_data = self.neutralized_factors[self.neutralized_factors['date'] == sample_date]
        
        print(f"验证日期: {sample_date.strftime('%Y-%m-%d')}")
        print(f"样本股票数: {len(sample_data)}")
        
        # 计算原始因子和中性化因子的行业均值
        original_by_industry = sample_data.groupby('l1_name')[factor_name].mean()
        neutralized_by_industry = sample_data.groupby('l1_name')[f'{factor_name}_neutralized'].mean()
        
        print(f"\n原始{factor_name}因子的行业差异:")
        print(f"行业间标准差: {original_by_industry.std():.4f}")
        print(f"最大值: {original_by_industry.max():.4f}")
        print(f"最小值: {original_by_industry.min():.4f}")
        
        print(f"\n中性化后{factor_name}因子的行业差异:")
        print(f"行业间标准差: {neutralized_by_industry.std():.6f}")
        print(f"最大值: {neutralized_by_industry.max():.6f}")
        print(f"最小值: {neutralized_by_industry.min():.6f}")
        
        print(f"\n✓ 中性化效果: 行业间差异从{original_by_industry.std():.4f}降至{neutralized_by_industry.std():.6f}")
    
    def construct_market_neutral_portfolio(self, factor_name='pb'):
        """
        构建市场中性组合
        
        策略逻辑：
        1. 使用中性化后的因子进行选股
        2. 基于中证800权重进行市场中性化
        3. 构建多空对冲组合
        """
        print(f"\n=== 构建基于{factor_name}的市场中性组合 ===")
        
        if self.neutralized_factors is None:
            print("请先进行行业中性化处理")
            return False
        
        # 获取月末日期列表
        monthly_dates = self.csi800_weights['trade_date'].unique()
        monthly_dates = sorted(monthly_dates)
        
        portfolio_results = []
        
        print(f"构建组合，共{len(monthly_dates)}个调仓日期")
        
        for i, rebalance_date in enumerate(monthly_dates):
            if i % 6 == 0:  # 每6个月打印一次进度
                print(f"处理进度: {i}/{len(monthly_dates)} ({rebalance_date.strftime('%Y-%m')})")
            
            # 获取当月中证800权重
            month_weights = self.csi800_weights[
                self.csi800_weights['trade_date'] == rebalance_date
            ].copy()
            
            if len(month_weights) == 0:
                continue
            
            # 获取最接近调仓日期的因子数据
            factor_date = self._find_closest_factor_date(rebalance_date)
            if factor_date is None:
                continue
            
            factor_data = self.neutralized_factors[
                self.neutralized_factors['date'] == factor_date
            ].copy()
            
            # 合并权重和因子数据
            merged = month_weights.merge(
                factor_data[['ts_code', f'{factor_name}_neutralized']], 
                on='ts_code', 
                how='inner'
            )
            
            if len(merged) < 50:  # 至少需要50只股票
                continue
            
            # 构建组合
            portfolio_result = self._build_monthly_portfolio(merged, factor_name, rebalance_date)
            if portfolio_result:
                portfolio_results.append(portfolio_result)
        
        if portfolio_results:
            self.portfolio_results = pd.DataFrame(portfolio_results)
            print(f"\n组合构建完成！共{len(self.portfolio_results)}期")
            return True
        else:
            print("组合构建失败")
            return False
    
    def _find_closest_factor_date(self, target_date):
        """找到最接近目标日期的因子数据日期"""
        factor_dates = self.neutralized_factors['date'].unique()
        
        # 找到小于等于目标日期的最近日期
        valid_dates = factor_dates[factor_dates <= target_date]
        
        if len(valid_dates) > 0:
            return max(valid_dates)
        else:
            return None
    
    def _build_monthly_portfolio(self, data, factor_name, rebalance_date):
        """构建单月组合"""
        try:
            # 按因子值排序
            data = data.sort_values(f'{factor_name}_neutralized', ascending=True)
            
            # 选择前30%做多，后30%做空
            n_stocks = len(data)
            n_long = int(n_stocks * 0.3)
            n_short = int(n_stocks * 0.3)
            
            long_stocks = data.head(n_long)
            short_stocks = data.tail(n_short)
            
            # 计算权重（基于中证800权重进行调整）
            long_total_weight = long_stocks['weight'].sum()
            short_total_weight = short_stocks['weight'].sum()
            
            # 标准化权重，使多头和空头权重之和都为1
            long_stocks = long_stocks.copy()
            short_stocks = short_stocks.copy()
            
            long_stocks['portfolio_weight'] = long_stocks['weight'] / long_total_weight
            short_stocks['portfolio_weight'] = short_stocks['weight'] / short_total_weight
            
            # 模拟收益率（基于因子效应）
            long_return = self._simulate_portfolio_return(long_stocks, factor_name, 'long')
            short_return = self._simulate_portfolio_return(short_stocks, factor_name, 'short')
            
            # 多空对冲收益
            portfolio_return = long_return - short_return
            
            return {
                'rebalance_date': rebalance_date,
                'long_return': long_return,
                'short_return': short_return,
                'portfolio_return': portfolio_return,
                'n_long': len(long_stocks),
                'n_short': len(short_stocks),
                'long_weight_sum': long_total_weight,
                'short_weight_sum': short_total_weight
            }
            
        except Exception as e:
            print(f"构建{rebalance_date}组合失败: {e}")
            return None
    
    def _simulate_portfolio_return(self, stocks, factor_name, position_type):
        """模拟组合收益率"""
        # 基于因子值模拟收益率
        factor_values = stocks[f'{factor_name}_neutralized'].values
        weights = stocks['portfolio_weight'].values
        
        # 因子效应：PB因子负效应（低PB高收益）
        if factor_name == 'pb':
            factor_effect = -0.002 * np.mean(factor_values)
        else:  # 市值因子
            factor_effect = -0.001 * np.mean(factor_values)
        
        # 添加随机噪音
        noise = np.random.normal(0, 0.01)
        
        # 加权平均收益率
        portfolio_return = factor_effect + noise
        
        return portfolio_return
    
    def analyze_and_visualize(self):
        """分析和可视化策略表现"""
        if self.portfolio_results is None:
            print("请先构建组合")
            return
        
        print(f"\n=== 策略表现分析 ===")
        
        # 计算关键指标
        returns = self.portfolio_results['portfolio_return'].values
        
        total_return = (1 + returns).prod() - 1
        annual_return = (1 + returns.mean()) ** 12 - 1  # 月度数据年化
        annual_vol = returns.std() * np.sqrt(12)
        sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
        max_drawdown = self._calculate_max_drawdown(returns)
        win_rate = (returns > 0).mean()
        
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"年化波动率: {annual_vol:.2%}")
        print(f"夏普比率: {sharpe_ratio:.3f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"胜率: {win_rate:.2%}")
        
        # 可视化
        self._create_visualizations()
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        return np.min(drawdown)
    
    def _create_visualizations(self):
        """创建可视化图表"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 1. 累计收益率
        cumulative_returns = (1 + self.portfolio_results['portfolio_return']).cumprod()
        axes[0, 0].plot(self.portfolio_results['rebalance_date'], cumulative_returns, 
                       linewidth=2, label='市场中性策略')
        axes[0, 0].set_title('策略累计收益率', fontsize=12, fontweight='bold')
        axes[0, 0].set_ylabel('累计收益率')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 多空收益对比
        axes[0, 1].plot(self.portfolio_results['rebalance_date'], 
                       self.portfolio_results['long_return'], 
                       label='多头收益', alpha=0.7)
        axes[0, 1].plot(self.portfolio_results['rebalance_date'], 
                       self.portfolio_results['short_return'], 
                       label='空头收益', alpha=0.7)
        axes[0, 1].plot(self.portfolio_results['rebalance_date'], 
                       self.portfolio_results['portfolio_return'], 
                       label='多空对冲', linewidth=2)
        axes[0, 1].set_title('多空收益对比', fontsize=12, fontweight='bold')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 收益率分布
        axes[1, 0].hist(self.portfolio_results['portfolio_return'], bins=20, 
                       alpha=0.7, edgecolor='black')
        axes[1, 0].set_title('月度收益率分布', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('月度收益率')
        axes[1, 0].set_ylabel('频数')
        
        # 4. 回撤分析
        cumulative = (1 + self.portfolio_results['portfolio_return']).cumprod()
        running_max = np.maximum.accumulate(cumulative)
        drawdown = (cumulative - running_max) / running_max
        
        axes[1, 1].fill_between(self.portfolio_results['rebalance_date'], 
                               drawdown, 0, alpha=0.3, color='red')
        axes[1, 1].set_title('策略回撤', fontsize=12, fontweight='bold')
        axes[1, 1].set_ylabel('回撤')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()


def main():
    """主函数 - 完整的市场中性策略流程"""
    print("完整市场中性策略构建")
    print("="*60)
    
    # 创建策略对象
    strategy = MarketNeutralStrategy()
    
    # 1. 加载所有数据
    if not strategy.load_all_data():
        print("数据加载失败，程序退出")
        return
    
    # 2. 行业中性化处理
    print(f"\n{'='*60}")
    print("第二步：行业中性化处理")
    print(f"{'='*60}")
    
    if not strategy.industry_neutralization('pb'):
        print("行业中性化失败，程序退出")
        return
    
    # 3. 构建市场中性组合
    print(f"\n{'='*60}")
    print("第三步：构建市场中性组合")
    print(f"{'='*60}")
    
    if not strategy.construct_market_neutral_portfolio('pb'):
        print("组合构建失败，程序退出")
        return
    
    # 4. 分析和可视化
    print(f"\n{'='*60}")
    print("第四步：策略分析和可视化")
    print(f"{'='*60}")
    
    strategy.analyze_and_visualize()
    
    print(f"\n{'='*60}")
    print("市场中性策略构建完成！")
    print(f"{'='*60}")


if __name__ == "__main__":
    main()
