"""
指数中性和行业中性简单示例
用最直观的方式展示核心概念
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def main():
    print("指数中性和行业中性概念演示")
    print("="*40)
    
    # 加载数据
    print("\n1. 加载数据")
    weights_df = pd.read_csv(r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\CSI800stocks.csv")
    industry_df = pd.read_excel(r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\swind.xlsx")
    
    # 取最新数据
    latest_date = pd.to_datetime(weights_df['trade_date']).max()
    latest_weights = weights_df[pd.to_datetime(weights_df['trade_date']) == latest_date]
    industry_df = industry_df.sort_values('in_date').groupby('ts_code').last().reset_index()
    
    print(f"数据日期: {latest_date.strftime('%Y-%m-%d')}")
    print(f"成分股数量: {len(latest_weights)}")
    
    # 合并数据
    merged = latest_weights.merge(industry_df[['ts_code', 'l1_name']], on='ts_code', how='left')
    
    print("\n2. 指数权重分析")
    print("前10大权重股:")
    top10 = merged.nlargest(10, 'weight')
    for _, row in top10.iterrows():
        industry = row['l1_name'] if pd.notna(row['l1_name']) else '未知'
        print(f"  {row['ts_code']}: {row['weight']:.2f}% ({industry})")
    
    print("\n3. 行业权重分析")
    industry_weights = merged.groupby('l1_name')['weight'].sum().sort_values(ascending=False)
    print("前10大行业权重:")
    for industry, weight in industry_weights.head(10).items():
        print(f"  {industry}: {weight:.2f}%")
    
    print("\n4. 指数中性示例")
    print("-"*30)
    
    # 假设选择5只股票做多
    selected_stocks = ['601318.SH', '600519.SH', '300750.SZ', '600036.SH', '601166.SH']
    
    print("假设我们看好这5只股票，想要做多:")
    for stock in selected_stocks:
        stock_info = merged[merged['ts_code'] == stock]
        if not stock_info.empty:
            weight = stock_info.iloc[0]['weight']
            industry = stock_info.iloc[0]['l1_name'] if pd.notna(stock_info.iloc[0]['l1_name']) else '未知'
            print(f"  {stock}: 指数权重{weight:.2f}% ({industry})")
    
    print("\n普通做多策略的问题:")
    print("  - 承担市场系统性风险")
    print("  - 如果市场下跌，即使选股正确也可能亏损")
    
    print("\n指数中性解决方案:")
    print("  - 做多选中的股票")
    print("  - 同时按指数权重做空中证800")
    print("  - 结果：净市场暴露≈0，只赚取选股Alpha")
    
    # 简单计算示例
    long_amount = 100  # 假设做多100万
    print(f"\n具体操作示例（假设投入{long_amount}万）:")
    print(f"  多头：等权重买入5只股票，每只{long_amount/5}万")
    print(f"  空头：按指数权重卖空中证800，总金额{long_amount}万")
    print(f"  净暴露：{long_amount} - {long_amount} = 0万（市场中性）")
    
    print("\n5. 行业中性示例")
    print("-"*30)
    
    # 分析选中股票的行业分布
    selected_industries = {}
    for stock in selected_stocks:
        stock_info = merged[merged['ts_code'] == stock]
        if not stock_info.empty:
            industry = stock_info.iloc[0]['l1_name']
            if pd.notna(industry):
                if industry in selected_industries:
                    selected_industries[industry] += 1
                else:
                    selected_industries[industry] = 1
    
    print("选中股票的行业分布:")
    for industry, count in selected_industries.items():
        print(f"  {industry}: {count}只股票")
    
    print("\n行业中性的思路:")
    print("  - 如果我们在银行业选了3只股票做多")
    print("  - 就应该在银行业内选其他股票做空")
    print("  - 确保银行业内部多空平衡")
    print("  - 这样就消除了银行业整体涨跌的影响")
    
    print("\n6. 两种方法的区别")
    print("-"*30)
    print("指数中性:")
    print("  ✓ 消除整个市场的系统性风险")
    print("  ✓ 对冲市场涨跌")
    print("  ✗ 仍可能受行业轮动影响")
    
    print("\n行业中性:")
    print("  ✓ 消除行业配置的影响")
    print("  ✓ 对冲行业轮动风险")
    print("  ✗ 仍可能受市场整体影响")
    
    print("\n指数+行业双重中性:")
    print("  ✓ 同时消除市场和行业风险")
    print("  ✓ 最纯粹的个股选择收益")
    print("  ✗ 可能过度中性化，限制收益空间")
    
    print("\n7. 实际应用考虑")
    print("-"*30)
    print("优点:")
    print("  - 风险控制：有效降低系统性风险")
    print("  - 收益稳定：不依赖市场方向")
    print("  - 适合震荡市：市场无明确趋势时有效")
    
    print("\n挑战:")
    print("  - 交易成本：需要频繁调仓")
    print("  - 流动性：大量空头可能面临借券困难")
    print("  - 选股能力：完全依赖Alpha获取能力")
    
    print("\n" + "="*40)
    print("总结：指数中性是通过对冲消除不想要的风险，")
    print("专注于获取想要的收益（选股Alpha）的策略思路")
    print("="*40)

if __name__ == "__main__":
    main()
