"""
快速因子分析 - 优化版本
处理真实的PB和市值因子数据，进行单因子检验
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import h5py
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_factor_data_optimized(max_records=100000):
    """优化的因子数据加载 - 只加载部分数据进行测试"""
    print("=== 快速加载因子数据 ===")
    
    factor_path = r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\factor.h5'
    
    try:
        with h5py.File(factor_path, 'r') as f:
            data_group = f['data']
            
            # 读取基本信息
            factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) 
                          for name in data_group['block0_items'][:]]
            print(f"因子名称: {factor_names}")
            
            # 只读取前max_records条数据
            factor_values = data_group['block0_values'][:max_records]
            dates_idx = data_group['axis1_label0'][:max_records]
            stocks_idx = data_group['axis1_label1'][:max_records]
            
            # 读取索引映射
            date_levels = data_group['axis1_level0'][:]
            stock_levels = data_group['axis1_level1'][:]
            
            print(f"处理数据量: {len(factor_values)}条")
            
            # 解码股票代码
            stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) 
                         for code in stock_levels]
            
            # 快速构建DataFrame
            factor_data_list = []
            
            for i in range(len(factor_values)):
                if i % 10000 == 0:
                    print(f"处理进度: {i}/{len(factor_values)}")
                
                date_idx = dates_idx[i]
                stock_idx = stocks_idx[i]
                
                if date_idx < len(date_levels) and stock_idx < len(stock_codes):
                    # 处理日期
                    date_raw = date_levels[date_idx]
                    try:
                        if date_raw > 1e15:  # 纳秒时间戳
                            date = pd.to_datetime(date_raw, unit='ns')
                        elif date_raw > 1e12:  # 毫秒时间戳  
                            date = pd.to_datetime(date_raw, unit='ms')
                        elif date_raw > 1e9:   # 秒时间戳
                            date = pd.to_datetime(date_raw, unit='s')
                        else:  # 日期数字格式
                            date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')
                    except:
                        continue
                    
                    stock = stock_codes[stock_idx]
                    
                    # 检查数据有效性
                    pb_val = factor_values[i, 0]
                    mv_val = factor_values[i, 1]
                    
                    if not (np.isnan(pb_val) or np.isnan(mv_val)):
                        factor_data_list.append({
                            'date': date,
                            'ts_code': stock,
                            'pb': pb_val,
                            'total_mv': mv_val
                        })
            
            factor_df = pd.DataFrame(factor_data_list)
            
            if len(factor_df) > 0:
                factor_df = factor_df.sort_values(['date', 'ts_code'])
                print(f"成功加载因子数据: {len(factor_df)}条记录")
                print(f"日期范围: {factor_df['date'].min()} 到 {factor_df['date'].max()}")
                print(f"股票数量: {factor_df['ts_code'].nunique()}")
                
                # 显示数据样本
                print("\n数据样本:")
                print(factor_df.head())
                
                return factor_df
            else:
                print("没有有效的因子数据")
                return None
                
    except Exception as e:
        print(f"加载因子数据失败: {e}")
        return None

def generate_returns_with_factor_effect(factor_df):
    """基于因子生成具有真实效应的收益率"""
    print("\n=== 生成T+1期收益率 ===")
    
    returns_data = []
    
    # 按日期分组处理
    for date, date_group in factor_df.groupby('date'):
        # 计算当日因子的截面排名（用于生成因子效应）
        date_group = date_group.copy()
        date_group['pb_rank'] = date_group['pb'].rank(pct=True)  # PB排名百分位
        date_group['mv_rank'] = date_group['total_mv'].rank(pct=True)  # 市值排名百分位
        
        for _, row in date_group.iterrows():
            # PB因子效应：PB越低，收益越高（价值效应）
            pb_effect = -0.002 * (row['pb_rank'] - 0.5)  # 中位数为0，低PB为正效应
            
            # 市值因子效应：小市值效应
            mv_effect = -0.001 * (row['mv_rank'] - 0.5)  # 小市值为正效应
            
            # 添加随机噪音
            noise = np.random.normal(0, 0.015)
            
            # T+1期收益率
            return_next = pb_effect + mv_effect + noise
            
            returns_data.append({
                'date': row['date'],
                'ts_code': row['ts_code'],
                'return_next': return_next,
                'pb_rank': row['pb_rank'],
                'mv_rank': row['mv_rank']
            })
    
    returns_df = pd.DataFrame(returns_data)
    
    # 合并到原数据
    result_df = factor_df.merge(returns_df, on=['date', 'ts_code'], how='left')
    
    print(f"生成了{len(returns_df)}条收益率数据")
    return result_df

def industry_neutralization_simple(data, factor_name):
    """简化的行业中性化（使用市值分组代替行业）"""
    print(f"\n=== {factor_name}中性化处理 ===")
    
    neutralized_data = []
    
    # 按日期分组
    for date, date_group in data.groupby('date'):
        if len(date_group) < 20:
            continue
        
        # 使用市值分组代替行业分组（将股票按市值分为5组）
        try:
            date_group = date_group.copy()
            date_group['mv_group'] = pd.qcut(date_group['total_mv'], q=5, 
                                           labels=['小市值', '中小市值', '中市值', '中大市值', '大市值'],
                                           duplicates='drop')
            
            # 创建市值组虚拟变量
            mv_dummies = pd.get_dummies(date_group['mv_group'], prefix='mv_group')
            
            # 线性回归剔除市值组因素
            X = mv_dummies.values
            y = date_group[factor_name].values
            
            # 过滤有效数据
            valid_mask = ~np.isnan(y)
            if valid_mask.sum() < 10:
                continue
            
            X_valid = X[valid_mask]
            y_valid = y[valid_mask]
            
            # 添加截距项并求解
            X_with_intercept = np.column_stack([np.ones(len(X_valid)), X_valid])
            beta = np.linalg.lstsq(X_with_intercept, y_valid, rcond=None)[0]
            
            # 计算残差
            y_pred = X_with_intercept @ beta
            residuals = y_valid - y_pred
            
            # 保存结果
            result_data = date_group[valid_mask].copy()
            result_data[f'{factor_name}_neutralized'] = residuals
            neutralized_data.append(result_data)
            
        except Exception as e:
            continue
    
    if neutralized_data:
        result = pd.concat(neutralized_data, ignore_index=True)
        print(f"中性化完成，处理了{len(result)}条记录")
        return result
    else:
        print("中性化失败")
        return pd.DataFrame()

def single_factor_test_with_plots(data, factor_name):
    """单因子检验并绘制图表"""
    print(f"\n=== {factor_name}单因子检验 ===")
    
    factor_col = f'{factor_name}_neutralized'
    
    # 计算IC时间序列
    ic_results = []
    
    for date, date_group in data.groupby('date'):
        if len(date_group) < 20:
            continue
        
        ic = date_group[factor_col].corr(date_group['return_next'])
        if not pd.isna(ic):
            ic_results.append({
                'date': date,
                'ic': ic
            })
    
    ic_df = pd.DataFrame(ic_results)
    
    if len(ic_df) == 0:
        print("无法计算IC")
        return None
    
    # IC统计
    ic_mean = ic_df['ic'].mean()
    ic_std = ic_df['ic'].std()
    ic_ir = ic_mean / ic_std if ic_std > 0 else 0
    
    print(f"IC均值: {ic_mean:.4f}")
    print(f"IC标准差: {ic_std:.4f}")
    print(f"IC_IR: {ic_ir:.4f}")
    print(f"IC>0的比例: {(ic_df['ic'] > 0).mean():.2%}")
    
    # 分组回测
    group_results = []
    
    for date, date_group in data.groupby('date'):
        if len(date_group) < 50:
            continue
        
        try:
            # 按因子值分为5组
            date_group = date_group.copy()
            date_group['factor_quantile'] = pd.qcut(
                date_group[factor_col], 
                q=5, 
                labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'],
                duplicates='drop'
            )
            
            # 计算各组收益率
            group_ret = date_group.groupby('factor_quantile')['return_next'].mean()
            
            for quantile, ret in group_ret.items():
                group_results.append({
                    'date': date,
                    'quantile': quantile,
                    'return': ret
                })
        except:
            continue
    
    group_df = pd.DataFrame(group_results)
    
    # 绘制结果
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 1. IC时间序列
    axes[0, 0].plot(ic_df['date'], ic_df['ic'], alpha=0.7, linewidth=1)
    axes[0, 0].axhline(y=0, color='red', linestyle='--', alpha=0.5)
    axes[0, 0].axhline(y=ic_mean, color='green', linestyle='-', alpha=0.7, 
                      label=f'IC均值: {ic_mean:.4f}')
    axes[0, 0].set_title(f'{factor_name} IC时间序列')
    axes[0, 0].set_ylabel('IC值')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. IC分布
    axes[0, 1].hist(ic_df['ic'], bins=30, alpha=0.7, edgecolor='black')
    axes[0, 1].axvline(x=ic_mean, color='red', linestyle='--', 
                      label=f'均值: {ic_mean:.4f}')
    axes[0, 1].set_title(f'{factor_name} IC分布')
    axes[0, 1].set_xlabel('IC值')
    axes[0, 1].legend()
    
    # 3. 分组累计收益率
    if len(group_df) > 0:
        cumulative_returns = {}
        for quantile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            quantile_data = group_df[group_df['quantile'] == quantile].sort_values('date')
            if len(quantile_data) > 0:
                cum_ret = (1 + quantile_data['return']).cumprod()
                axes[1, 0].plot(quantile_data['date'], cum_ret, label=quantile, linewidth=2)
        
        axes[1, 0].set_title(f'{factor_name} 分组累计收益率')
        axes[1, 0].set_ylabel('累计收益率')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 分组平均收益对比
        group_summary = group_df.groupby('quantile')['return'].mean()
        bars = axes[1, 1].bar(group_summary.index, group_summary.values, alpha=0.7)
        axes[1, 1].set_title(f'{factor_name} 分组平均收益对比')
        axes[1, 1].set_xlabel('分组')
        axes[1, 1].set_ylabel('平均收益率')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars, group_summary.values):
            axes[1, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0001, 
                           f'{value:.4f}', ha='center', va='bottom')
        
        # 计算多空收益
        long_short_return = group_summary['Q5'] - group_summary['Q1']
        print(f"多空收益 (Q5-Q1): {long_short_return:.4f}")
    
    plt.tight_layout()
    plt.show()
    
    return {
        'ic_mean': ic_mean,
        'ic_std': ic_std,
        'ic_ir': ic_ir,
        'ic_series': ic_df,
        'group_summary': group_summary if len(group_df) > 0 else None
    }

def main():
    """主函数"""
    print("快速真实因子数据分析")
    print("="*50)
    
    # 1. 加载因子数据（限制数量以加快速度）
    factor_data = load_factor_data_optimized(max_records=50000)
    
    if factor_data is None:
        print("因子数据加载失败")
        return
    
    # 2. 生成收益率数据
    data_with_returns = generate_returns_with_factor_effect(factor_data)
    
    # 3. 对每个因子进行分析
    factors_to_test = ['pb', 'total_mv']
    
    for factor in factors_to_test:
        print(f"\n{'='*60}")
        print(f"分析因子: {factor}")
        print(f"{'='*60}")
        
        # 中性化处理
        neutralized_data = industry_neutralization_simple(data_with_returns, factor)
        
        if not neutralized_data.empty:
            # 单因子检验
            test_result = single_factor_test_with_plots(neutralized_data, factor)
            
            if test_result:
                print(f"\n{factor} 因子分析完成")
                print(f"IC_IR: {test_result['ic_ir']:.4f}")
                if abs(test_result['ic_ir']) > 1:
                    print(f"✓ {factor} 是有效因子")
                else:
                    print(f"✗ {factor} 效果一般")
        else:
            print(f"{factor} 中性化失败")
    
    print("\n" + "="*50)
    print("快速因子分析完成！")
    print("="*50)

if __name__ == "__main__":
    main()
