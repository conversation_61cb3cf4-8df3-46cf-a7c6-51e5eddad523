"""
指数中性计算模块
Index Neutral Calculator Module

功能：
1. 计算指数中性权重
2. 风险暴露分析
3. 组合优化
4. 回测计算
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

class NeutralCalculator:
    """指数中性计算器"""
    
    def __init__(self, index_weights, industry_data, stock_returns=None):
        """
        初始化计算器
        
        Parameters:
        -----------
        index_weights : pd.DataFrame
            指数权重数据
        industry_data : pd.DataFrame
            行业分类数据
        stock_returns : pd.DataFrame, optional
            股票收益率数据
        """
        self.index_weights = index_weights
        self.industry_data = industry_data
        self.stock_returns = stock_returns
        
        # 预处理数据
        self._prepare_data()
    
    def _prepare_data(self):
        """数据预处理"""
        # 创建股票-行业映射
        self.stock_industry_map = self.industry_data.set_index('ts_code')['l1_name'].to_dict()
        
        # 获取所有调仓日期
        self.rebalance_dates = sorted(self.index_weights['trade_date'].unique())
        
        print(f"调仓日期数量: {len(self.rebalance_dates)}")
        print(f"日期范围: {self.rebalance_dates[0]} 到 {self.rebalance_dates[-1]}")
    
    def calculate_neutral_weights(self, long_stocks, long_weights=None, method='weight_based'):
        """
        计算指数中性权重
        
        Parameters:
        -----------
        long_stocks : list
            多头股票列表
        long_weights : dict, optional
            多头权重，如果为None则等权重
        method : str
            中性化方法 ('weight_based', 'beta_based', 'industry_neutral')
            
        Returns:
        --------
        dict
            每个调仓日期的权重字典
        """
        print(f"计算指数中性权重，方法: {method}")
        
        if long_weights is None:
            # 等权重分配
            weight_per_stock = 1.0 / len(long_stocks)
            long_weights = {stock: weight_per_stock for stock in long_stocks}
        
        neutral_weights = {}
        
        for date in self.rebalance_dates:
            # 获取当期指数权重
            current_index_weights = self._get_index_weights_at_date(date)
            
            if current_index_weights is None:
                continue
            
            if method == 'weight_based':
                weights = self._weight_based_neutral(long_weights, current_index_weights)
            elif method == 'beta_based':
                weights = self._beta_based_neutral(long_weights, current_index_weights, date)
            elif method == 'industry_neutral':
                weights = self._industry_neutral(long_weights, current_index_weights)
            else:
                raise ValueError(f"未知的中性化方法: {method}")
            
            neutral_weights[date] = weights
        
        return neutral_weights
    
    def _get_index_weights_at_date(self, date):
        """获取指定日期的指数权重"""
        weights_data = self.index_weights[self.index_weights['trade_date'] == date]
        if len(weights_data) == 0:
            return None
        
        # 转换为字典格式，权重转换为小数
        weights = weights_data.set_index('ts_code')['weight'].to_dict()
        weights = {k: v/100 for k, v in weights.items()}  # 百分比转小数
        
        return weights
    
    def _weight_based_neutral(self, long_weights, index_weights):
        """基于权重的中性化"""
        neutral_weights = {}
        
        # 计算多头总权重
        total_long_weight = sum(long_weights.values())
        
        # 多头权重
        for stock, weight in long_weights.items():
            neutral_weights[stock] = weight
        
        # 空头权重（按指数权重比例分配）
        for stock, index_weight in index_weights.items():
            if stock in neutral_weights:
                # 对于多头股票，空头权重 = 指数权重 * 总多头权重 - 多头权重
                short_weight = index_weight * total_long_weight - long_weights.get(stock, 0)
                neutral_weights[stock] += short_weight
            else:
                # 对于非多头股票，完全按指数权重做空
                neutral_weights[stock] = -index_weight * total_long_weight
        
        return neutral_weights
    
    def _beta_based_neutral(self, long_weights, index_weights, date):
        """基于Beta的中性化"""
        # 这里需要计算个股相对指数的Beta
        # 由于缺少收益率数据，暂时使用权重方法
        return self._weight_based_neutral(long_weights, index_weights)
    
    def _industry_neutral(self, long_weights, index_weights):
        """行业中性化"""
        neutral_weights = {}
        
        # 计算每个行业的多头权重
        industry_long_weights = {}
        for stock, weight in long_weights.items():
            industry = self.stock_industry_map.get(stock, '其他')
            industry_long_weights[industry] = industry_long_weights.get(industry, 0) + weight
        
        # 计算每个行业的指数权重
        industry_index_weights = {}
        for stock, weight in index_weights.items():
            industry = self.stock_industry_map.get(stock, '其他')
            industry_index_weights[industry] = industry_index_weights.get(industry, 0) + weight
        
        # 多头权重
        for stock, weight in long_weights.items():
            neutral_weights[stock] = weight
        
        # 行业内中性化
        for stock, index_weight in index_weights.items():
            industry = self.stock_industry_map.get(stock, '其他')
            
            if industry in industry_long_weights:
                # 计算该行业需要的空头权重
                industry_long = industry_long_weights[industry]
                industry_index = industry_index_weights[industry]
                
                if industry_index > 0:
                    # 按行业内权重比例分配空头
                    stock_ratio = index_weight / industry_index
                    short_weight = -industry_long * stock_ratio
                    
                    if stock in neutral_weights:
                        neutral_weights[stock] += short_weight
                    else:
                        neutral_weights[stock] = short_weight
        
        return neutral_weights
    
    def calculate_risk_exposure(self, weights, date):
        """
        计算风险暴露
        
        Parameters:
        -----------
        weights : dict
            股票权重
        date : datetime
            计算日期
            
        Returns:
        --------
        dict
            风险暴露指标
        """
        exposure = {
            'market_exposure': 0,
            'industry_exposure': {},
            'concentration_risk': 0,
            'long_short_ratio': 0
        }
        
        # 市场暴露（总权重）
        exposure['market_exposure'] = sum(weights.values())
        
        # 行业暴露
        for stock, weight in weights.items():
            industry = self.stock_industry_map.get(stock, '其他')
            exposure['industry_exposure'][industry] = exposure['industry_exposure'].get(industry, 0) + weight
        
        # 集中度风险（前10大持仓权重）
        sorted_weights = sorted(weights.values(), key=abs, reverse=True)
        exposure['concentration_risk'] = sum(sorted_weights[:10])
        
        # 多空比例
        long_weight = sum([w for w in weights.values() if w > 0])
        short_weight = abs(sum([w for w in weights.values() if w < 0]))
        if short_weight > 0:
            exposure['long_short_ratio'] = long_weight / short_weight
        
        return exposure
    
    def backtest_strategy(self, neutral_weights, start_date=None, end_date=None):
        """
        回测策略
        
        Parameters:
        -----------
        neutral_weights : dict
            中性权重字典
        start_date : datetime, optional
            开始日期
        end_date : datetime, optional
            结束日期
            
        Returns:
        --------
        pd.DataFrame
            回测结果
        """
        print("开始回测...")
        
        if self.stock_returns is None:
            print("缺少收益率数据，生成模拟数据进行回测")
            return self._simulate_backtest(neutral_weights)
        
        # 实际回测逻辑
        results = []
        
        for i, date in enumerate(self.rebalance_dates):
            if date not in neutral_weights:
                continue
            
            weights = neutral_weights[date]
            
            # 计算下一期收益（到下一个调仓日或期末）
            next_date = self.rebalance_dates[i+1] if i+1 < len(self.rebalance_dates) else None
            period_return = self._calculate_period_return(weights, date, next_date)
            
            # 计算风险指标
            risk_exposure = self.calculate_risk_exposure(weights, date)
            
            results.append({
                'date': date,
                'return': period_return,
                'market_exposure': risk_exposure['market_exposure'],
                'concentration_risk': risk_exposure['concentration_risk'],
                'long_short_ratio': risk_exposure['long_short_ratio']
            })
        
        return pd.DataFrame(results)
    
    def _simulate_backtest(self, neutral_weights):
        """模拟回测（用于演示）"""
        results = []
        
        for date in self.rebalance_dates:
            if date not in neutral_weights:
                continue
            
            weights = neutral_weights[date]
            
            # 模拟收益率
            # 假设指数中性策略的收益率服从正态分布
            period_return = np.random.normal(0.002, 0.015)  # 年化约5%收益，15%波动
            
            # 计算风险指标
            risk_exposure = self.calculate_risk_exposure(weights, date)
            
            results.append({
                'date': date,
                'return': period_return,
                'market_exposure': risk_exposure['market_exposure'],
                'concentration_risk': risk_exposure['concentration_risk'],
                'long_short_ratio': risk_exposure['long_short_ratio'],
                'num_positions': len([w for w in weights.values() if abs(w) > 0.001])
            })
        
        return pd.DataFrame(results)
    
    def _calculate_period_return(self, weights, start_date, end_date):
        """计算期间收益率"""
        # 这里需要实际的收益率数据
        # 暂时返回模拟值
        return np.random.normal(0.002, 0.015)
    
    def analyze_performance(self, backtest_results):
        """分析策略表现"""
        if len(backtest_results) == 0:
            print("没有回测结果")
            return {}
        
        returns = backtest_results['return']
        
        # 计算关键指标
        metrics = {
            '总收益率': (1 + returns).prod() - 1,
            '年化收益率': (1 + returns.mean()) ** 252 - 1,
            '年化波动率': returns.std() * np.sqrt(252),
            '夏普比率': returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0,
            '最大回撤': self._calculate_max_drawdown(returns),
            '胜率': (returns > 0).mean(),
            '平均市场暴露': backtest_results['market_exposure'].mean(),
            '平均多空比例': backtest_results['long_short_ratio'].mean()
        }
        
        return metrics
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def plot_results(self, backtest_results):
        """绘制回测结果"""
        if len(backtest_results) == 0:
            print("没有回测结果可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 累计收益率
        cumulative_returns = (1 + backtest_results['return']).cumprod()
        axes[0, 0].plot(backtest_results['date'], cumulative_returns)
        axes[0, 0].set_title('累计收益率')
        axes[0, 0].set_ylabel('累计收益率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 市场暴露
        axes[0, 1].plot(backtest_results['date'], backtest_results['market_exposure'])
        axes[0, 1].axhline(y=0, color='r', linestyle='--', alpha=0.5)
        axes[0, 1].set_title('市场暴露度')
        axes[0, 1].set_ylabel('暴露度')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 收益率分布
        axes[1, 0].hist(backtest_results['return'], bins=30, alpha=0.7)
        axes[1, 0].set_title('收益率分布')
        axes[1, 0].set_xlabel('期间收益率')
        
        # 多空比例
        axes[1, 1].plot(backtest_results['date'], backtest_results['long_short_ratio'])
        axes[1, 1].axhline(y=1, color='r', linestyle='--', alpha=0.5)
        axes[1, 1].set_title('多空比例')
        axes[1, 1].set_ylabel('多空比例')
        axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.show()


def main():
    """测试中性计算功能"""
    print("=== 指数中性计算测试 ===\n")
    
    # 这里需要实际的数据
    # 暂时创建示例数据进行测试
    print("创建示例数据...")
    
    # 示例权重数据
    dates = pd.date_range('2023-01-31', '2023-12-31', freq='M')
    stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    index_weights = []
    for date in dates:
        for i, stock in enumerate(stocks):
            weight = np.random.uniform(1, 5)  # 随机权重1-5%
            index_weights.append({
                'trade_date': date,
                'ts_code': stock,
                'weight': weight
            })
    
    index_weights_df = pd.DataFrame(index_weights)
    
    # 示例行业数据
    industry_data = pd.DataFrame({
        'ts_code': stocks,
        'l1_name': ['银行', '房地产', '银行', '银行', '非银金融']
    })
    
    # 创建计算器
    calculator = NeutralCalculator(index_weights_df, industry_data)
    
    # 计算中性权重
    long_stocks = ['000001.SZ', '600000.SH']
    neutral_weights = calculator.calculate_neutral_weights(long_stocks, method='weight_based')
    
    print(f"计算了{len(neutral_weights)}个调仓期的权重")
    
    # 回测
    backtest_results = calculator.backtest_strategy(neutral_weights)
    
    # 分析表现
    metrics = calculator.analyze_performance(backtest_results)
    print("\n=== 策略表现 ===")
    for key, value in metrics.items():
        if key in ['总收益率', '年化收益率', '年化波动率', '最大回撤']:
            print(f"{key}: {value:.2%}")
        elif key == '胜率':
            print(f"{key}: {value:.2%}")
        else:
            print(f"{key}: {value:.3f}")
    
    # 绘制结果
    calculator.plot_results(backtest_results)


if __name__ == "__main__":
    main()
