"""
配置文件
Configuration File

包含所有路径和参数配置
"""

import os

# 数据路径配置
DATA_BASE_PATH = r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data'

# 数据文件路径
DATA_FILES = {
    'stock_data': os.path.join(DATA_BASE_PATH, 'merged_data818.h5'),
    'index_weights': os.path.join(DATA_BASE_PATH, 'CSI800stocks.csv'),
    'industry_data': os.path.join(DATA_BASE_PATH, 'swind.xlsx')
}

# 策略参数配置
STRATEGY_CONFIG = {
    # 默认多头股票池（权重最大的5只股票）
    'default_long_stocks': ['601318.SH', '600519.SH', '300750.SZ', '600036.SH', '601166.SH'],
    
    # 中性化方法
    'neutralization_methods': ['weight_based', 'industry_neutral'],
    'default_method': 'weight_based',
    
    # 调仓频率
    'rebalance_frequency': 'M',  # 'D'=日度, 'W'=周度, 'M'=月度
    
    # 风险控制参数
    'max_single_stock_weight': 0.1,  # 单只股票最大权重
    'max_industry_exposure': 0.05,   # 单个行业最大暴露
    'target_market_exposure': 0.0,   # 目标市场暴露度
}

# 回测参数配置
BACKTEST_CONFIG = {
    'start_date': '2015-12-31',
    'end_date': '2025-05-30',
    'initial_capital': 1000000,  # 初始资金100万
    'transaction_cost': 0.001,   # 交易成本0.1%
}

# 输出配置
OUTPUT_CONFIG = {
    'save_results': True,
    'output_path': r'C:\Users\<USER>\Desktop\金元顺安\指数中性\results',
    'plot_style': 'seaborn',
    'figure_size': (15, 10),
    'dpi': 300
}

# 日志配置
LOG_CONFIG = {
    'log_level': 'INFO',
    'log_file': os.path.join(DATA_BASE_PATH, '..', 'logs', 'strategy.log'),
    'console_output': True
}

def get_data_path():
    """获取数据路径"""
    return DATA_BASE_PATH

def get_file_path(file_type):
    """
    获取指定类型文件的完整路径
    
    Parameters:
    -----------
    file_type : str
        文件类型 ('stock_data', 'index_weights', 'industry_data')
        
    Returns:
    --------
    str
        文件完整路径
    """
    return DATA_FILES.get(file_type, '')

def validate_paths():
    """验证所有路径是否存在"""
    issues = []
    
    # 检查数据目录
    if not os.path.exists(DATA_BASE_PATH):
        issues.append(f"数据目录不存在: {DATA_BASE_PATH}")
    
    # 检查数据文件
    for file_type, file_path in DATA_FILES.items():
        if not os.path.exists(file_path):
            issues.append(f"{file_type} 文件不存在: {file_path}")
    
    return issues

if __name__ == "__main__":
    # 测试配置
    print("=== 配置信息 ===")
    print(f"数据路径: {DATA_BASE_PATH}")
    print(f"股票数据: {DATA_FILES['stock_data']}")
    print(f"权重数据: {DATA_FILES['index_weights']}")
    print(f"行业数据: {DATA_FILES['industry_data']}")
    
    print("\n=== 路径验证 ===")
    issues = validate_paths()
    if issues:
        print("发现问题:")
        for issue in issues:
            print(f"- {issue}")
    else:
        print("所有路径验证通过")
