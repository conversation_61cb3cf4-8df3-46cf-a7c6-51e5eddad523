"""
指数中性策略实现
Index Neutral Strategy Implementation

主要功能：
1. 数据加载和预处理
2. 指数中性组合构建
3. 回测分析
4. 风险管理
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class IndexNeutralStrategy:
    """指数中性策略类"""
    
    def __init__(self, data_path=r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data'):
        """
        初始化策略

        Parameters:
        -----------
        data_path : str
            数据文件路径
        """
        self.data_path = data_path
        self.stock_data = None
        self.index_weights = None
        self.industry_data = None
        self.returns = None
        self.index_returns = None
        
    def load_data(self):
        """加载所有必要的数据"""
        print("正在加载数据...")
        
        try:
            # 加载A股日度数据
            print("加载A股日度数据...")
            # 由于HDF5文件需要特殊处理，我们先尝试其他方法
            # self.stock_data = pd.read_hdf(f'{self.data_path}merged_data818.h5', key='data')
            
            # 加载中证800权重数据
            print("加载中证800权重数据...")
            self.index_weights = pd.read_csv(f'{self.data_path}CSI800stocks.csv')
            self.index_weights['trade_date'] = pd.to_datetime(self.index_weights['trade_date'])
            
            # 加载行业数据
            print("加载行业数据...")
            self.industry_data = pd.read_excel(f'{self.data_path}swind.xlsx')
            
            print("数据加载完成！")
            self._preprocess_data()
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
            
        return True
    
    def _preprocess_data(self):
        """数据预处理"""
        print("开始数据预处理...")
        
        # 处理中证800权重数据
        self.index_weights = self.index_weights.sort_values(['trade_date', 'ts_code'])
        
        # 处理行业数据
        # 选择最新的行业分类
        self.industry_data = self.industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()
        
        print("数据预处理完成！")
    
    def calculate_returns(self, price_col='close'):
        """
        计算收益率
        
        Parameters:
        -----------
        price_col : str
            价格列名
        """
        if self.stock_data is None:
            print("请先加载股票数据")
            return
            
        print("计算收益率...")
        
        # 计算个股收益率
        self.returns = self.stock_data.groupby('ts_code')[price_col].pct_change()
        
        # 计算指数收益率（按权重加权）
        self._calculate_index_returns()
        
        print("收益率计算完成！")
    
    def _calculate_index_returns(self):
        """计算指数收益率"""
        # 这里需要根据实际数据结构来实现
        # 暂时用简单的等权重方法
        pass
    
    def build_neutral_portfolio(self, long_stocks, rebalance_freq='M'):
        """
        构建指数中性组合
        
        Parameters:
        -----------
        long_stocks : list
            多头股票列表
        rebalance_freq : str
            调仓频率 ('D', 'W', 'M')
        """
        print("构建指数中性组合...")
        
        # 获取调仓日期
        rebalance_dates = self._get_rebalance_dates(rebalance_freq)
        
        portfolio_returns = []
        
        for date in rebalance_dates:
            # 获取当期权重
            current_weights = self._get_weights_at_date(date)
            
            if current_weights is not None:
                # 构建多头组合
                long_portfolio = self._build_long_portfolio(long_stocks, date)
                
                # 构建空头对冲
                short_portfolio = self._build_short_hedge(current_weights, long_portfolio)
                
                # 计算组合收益
                period_return = self._calculate_portfolio_return(
                    long_portfolio, short_portfolio, date
                )
                
                portfolio_returns.append({
                    'date': date,
                    'return': period_return,
                    'long_value': sum(long_portfolio.values()),
                    'short_value': sum(short_portfolio.values())
                })
        
        self.portfolio_returns = pd.DataFrame(portfolio_returns)
        print("指数中性组合构建完成！")
        
        return self.portfolio_returns
    
    def _get_rebalance_dates(self, freq):
        """获取调仓日期"""
        if self.index_weights is None:
            return []
            
        dates = self.index_weights['trade_date'].unique()
        
        if freq == 'M':
            # 月末调仓
            return sorted(dates)
        elif freq == 'W':
            # 周度调仓
            return sorted(dates)  # 简化处理
        else:
            # 日度调仓
            return sorted(dates)
    
    def _get_weights_at_date(self, date):
        """获取指定日期的权重"""
        weights = self.index_weights[self.index_weights['trade_date'] == date]
        if len(weights) == 0:
            return None
        return weights.set_index('ts_code')['weight'].to_dict()
    
    def _build_long_portfolio(self, stocks, date):
        """构建多头组合"""
        # 简单等权重分配
        weight_per_stock = 1.0 / len(stocks)
        return {stock: weight_per_stock for stock in stocks}
    
    def _build_short_hedge(self, index_weights, long_portfolio):
        """构建空头对冲"""
        # 按照指数权重构建空头
        total_long_value = sum(long_portfolio.values())
        
        short_portfolio = {}
        for stock, weight in index_weights.items():
            if stock in long_portfolio:
                # 对于多头股票，空头权重 = 指数权重 - 多头权重
                short_portfolio[stock] = -(weight/100 - long_portfolio[stock])
            else:
                # 对于非多头股票，完全按指数权重做空
                short_portfolio[stock] = -(weight/100) * total_long_value
        
        return short_portfolio
    
    def _calculate_portfolio_return(self, long_portfolio, short_portfolio, date):
        """计算组合收益率"""
        # 这里需要根据实际数据计算
        # 暂时返回随机值用于测试
        return np.random.normal(0.001, 0.02)
    
    def analyze_performance(self):
        """分析策略表现"""
        if not hasattr(self, 'portfolio_returns'):
            print("请先构建投资组合")
            return
            
        print("分析策略表现...")
        
        returns = self.portfolio_returns['return']
        
        # 计算关键指标
        metrics = {
            '年化收益率': returns.mean() * 252,
            '年化波动率': returns.std() * np.sqrt(252),
            '夏普比率': (returns.mean() * 252) / (returns.std() * np.sqrt(252)),
            '最大回撤': self._calculate_max_drawdown(returns),
            '胜率': (returns > 0).mean(),
            '总收益率': (1 + returns).cumprod().iloc[-1] - 1
        }
        
        # 打印结果
        print("\n=== 策略表现分析 ===")
        for key, value in metrics.items():
            if key in ['年化收益率', '年化波动率', '总收益率', '最大回撤']:
                print(f"{key}: {value:.2%}")
            elif key == '胜率':
                print(f"{key}: {value:.2%}")
            else:
                print(f"{key}: {value:.3f}")
        
        return metrics
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def plot_performance(self):
        """绘制策略表现图"""
        if not hasattr(self, 'portfolio_returns'):
            print("请先构建投资组合")
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        returns = self.portfolio_returns['return']
        cumulative_returns = (1 + returns).cumprod()
        
        # 累计收益率
        axes[0, 0].plot(self.portfolio_returns['date'], cumulative_returns)
        axes[0, 0].set_title('累计收益率')
        axes[0, 0].set_ylabel('累计收益率')
        
        # 收益率分布
        axes[0, 1].hist(returns, bins=50, alpha=0.7)
        axes[0, 1].set_title('收益率分布')
        axes[0, 1].set_xlabel('日收益率')
        
        # 回撤
        running_max = cumulative_returns.expanding().max()
        drawdown = (cumulative_returns - running_max) / running_max
        axes[1, 0].fill_between(self.portfolio_returns['date'], drawdown, 0, alpha=0.3)
        axes[1, 0].set_title('回撤')
        axes[1, 0].set_ylabel('回撤')
        
        # 多空头寸
        axes[1, 1].plot(self.portfolio_returns['date'], self.portfolio_returns['long_value'], label='多头')
        axes[1, 1].plot(self.portfolio_returns['date'], self.portfolio_returns['short_value'], label='空头')
        axes[1, 1].set_title('多空头寸')
        axes[1, 1].legend()
        
        plt.tight_layout()
        plt.show()
    
    def get_industry_exposure(self):
        """分析行业暴露度"""
        print("分析行业暴露度...")
        
        # 合并行业数据
        industry_exposure = {}
        
        # 这里需要根据实际持仓计算行业暴露
        # 暂时返回示例数据
        industries = ['银行', '非银金融', '房地产', '建筑装饰', '钢铁', '化工']
        for industry in industries:
            industry_exposure[industry] = np.random.uniform(-0.05, 0.05)
        
        return industry_exposure


def main():
    """主函数 - 策略执行示例"""
    print("=== 指数中性策略分析 ===\n")
    
    # 初始化策略
    strategy = IndexNeutralStrategy()
    
    # 加载数据
    if not strategy.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 示例：选择一些股票作为多头组合
    long_stocks = ['000001.SZ', '000002.SZ', '600000.SH', '600036.SH', '000858.SZ']
    
    print(f"多头股票池: {long_stocks}")
    
    # 构建指数中性组合
    portfolio_returns = strategy.build_neutral_portfolio(long_stocks, rebalance_freq='M')
    
    # 分析表现
    metrics = strategy.analyze_performance()
    
    # 绘制图表
    strategy.plot_performance()
    
    # 行业暴露分析
    industry_exposure = strategy.get_industry_exposure()
    print("\n=== 行业暴露度 ===")
    for industry, exposure in industry_exposure.items():
        print(f"{industry}: {exposure:.2%}")


if __name__ == "__main__":
    main()
