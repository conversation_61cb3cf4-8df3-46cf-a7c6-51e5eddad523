"""
市场中性策略分析
Market Neutral Strategy Analysis

正确的理解：
1. 行业数据用于剔除行业因素，做行业中性化处理
2. 因子数据用于单因子检验，寻找有效的Alpha因子
3. 流程：行业中性化 → 单因子检验 → 构建市场中性策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class MarketNeutralStrategy:
    """市场中性策略分析类"""
    
    def __init__(self):
        """初始化"""
        self.stock_data = None
        self.factor_data = None
        self.industry_data = None
        self.index_weights = None
        self.neutralized_factors = None
        self.factor_test_results = {}
        
    def load_data(self):
        """加载数据"""
        print("=== 加载数据 ===")
        
        try:
            # 加载行业数据
            print("加载行业分类数据...")
            self.industry_data = pd.read_excel(r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\swind.xlsx')
            self.industry_data = self.industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()
            print(f"行业数据: {self.industry_data.shape[0]}只股票")
            
            # 加载中证800权重数据
            print("加载中证800权重数据...")
            self.index_weights = pd.read_csv(r'C:\Users\<USER>\Desktop\金元顺安\指数中性\data\CSI800stocks.csv')
            self.index_weights['trade_date'] = pd.to_datetime(self.index_weights['trade_date'])
            print(f"权重数据: {self.index_weights.shape[0]}条记录")
            
            # 模拟因子数据（由于无法直接读取HDF5）
            print("生成模拟因子数据...")
            self._generate_mock_factor_data()
            
            print("数据加载完成！")
            return True
            
        except Exception as e:
            print(f"数据加载失败: {e}")
            return False
    
    # 删除模拟因子数据方法
    def _generate_mock_factor_data(self):
        """生成模拟因子数据用于演示"""
        # 获取股票列表
        stocks = self.industry_data['ts_code'].unique()[:500]  # 取前500只股票
        dates = pd.date_range('2023-01-01', '2023-12-31', freq='D')
        
        # 生成模拟因子数据
        factor_data = []
        
        for date in dates:
            for stock in stocks:
                # 模拟常见因子
                factor_data.append({
                    'date': date,
                    'ts_code': stock,
                    'pe_ratio': np.random.lognormal(2.5, 0.5),  # 市盈率
                    'pb_ratio': np.random.lognormal(0.8, 0.3),  # 市净率
                    'roe': np.random.normal(0.12, 0.05),        # ROE
                    'roa': np.random.normal(0.06, 0.03),        # ROA
                    'debt_ratio': np.random.normal(0.4, 0.15),  # 资产负债率
                    'revenue_growth': np.random.normal(0.08, 0.12),  # 营收增长率
                    'momentum_20d': np.random.normal(0.02, 0.08),    # 20日动量
                    'volatility_20d': np.random.lognormal(-3, 0.5), # 20日波动率
                    'return_1d': np.random.normal(0.001, 0.02),      # 日收益率
                })
        
        self.factor_data = pd.DataFrame(factor_data)
        print(f"生成因子数据: {len(self.factor_data)}条记录，{len(stocks)}只股票")
    
    def industry_neutralization(self, factor_name):
        """
        行业中性化处理
        
        Parameters:
        -----------
        factor_name : str
            需要中性化的因子名称
        
        Returns:
        --------
        pd.DataFrame
            中性化后的因子数据
        """
        print(f"\n=== 对{factor_name}进行行业中性化 ===")
        
        # 合并因子数据和行业数据
        merged_data = self.factor_data.merge(
            self.industry_data[['ts_code', 'l1_name']], 
            on='ts_code', 
            how='left'
        )
        
        neutralized_data = []
        
        # 按日期分组进行中性化
        for date, date_group in merged_data.groupby('date'):
            # 为每个行业创建虚拟变量
            industry_dummies = pd.get_dummies(date_group['l1_name'], prefix='industry')
            
            # 准备回归数据
            X = industry_dummies.values
            y = date_group[factor_name].values
            
            # 过滤掉缺失值
            valid_mask = ~(np.isnan(y) | np.any(np.isnan(X), axis=1))
            if valid_mask.sum() < 10:  # 至少需要10个有效观测
                continue
                
            X_valid = X[valid_mask]
            y_valid = y[valid_mask]
            
            # 线性回归剔除行业因素（使用numpy实现）
            try:
                # 添加截距项
                X_with_intercept = np.column_stack([np.ones(len(X_valid)), X_valid])
                # 最小二乘法求解
                beta = np.linalg.lstsq(X_with_intercept, y_valid, rcond=None)[0]
                # 计算残差
                y_pred = X_with_intercept @ beta
                residuals = y_valid - y_pred
                
                # 保存中性化后的数据
                valid_data = date_group[valid_mask].copy()
                valid_data[f'{factor_name}_neutralized'] = residuals
                neutralized_data.append(valid_data[['date', 'ts_code', f'{factor_name}_neutralized']])
                
            except Exception as e:
                print(f"日期 {date} 回归失败: {e}")
                continue
        
        if neutralized_data:
            result = pd.concat(neutralized_data, ignore_index=True)
            print(f"行业中性化完成，处理了{len(result)}条记录")
            return result
        else:
            print("行业中性化失败")
            return pd.DataFrame()
    
    def single_factor_test(self, factor_name, neutralized_factor_data):
        """
        单因子检验
        
        Parameters:
        -----------
        factor_name : str
            因子名称
        neutralized_factor_data : pd.DataFrame
            中性化后的因子数据
        """
        print(f"\n=== {factor_name}单因子检验 ===")
        
        # 合并因子数据和收益率数据
        test_data = neutralized_factor_data.merge(
            self.factor_data[['date', 'ts_code', 'return_1d']], 
            on=['date', 'ts_code'], 
            how='inner'
        )
        
        if len(test_data) == 0:
            print("没有可用的测试数据")
            return
        
        # 计算因子与收益率的相关性
        factor_col = f'{factor_name}_neutralized'
        correlation = test_data[factor_col].corr(test_data['return_1d'])
        
        # 分组回测
        group_results = self._factor_group_backtest(test_data, factor_col)
        
        # IC分析
        ic_results = self._calculate_ic(test_data, factor_col)
        
        # 保存结果
        self.factor_test_results[factor_name] = {
            'correlation': correlation,
            'group_results': group_results,
            'ic_results': ic_results
        }
        
        print(f"因子与收益率相关性: {correlation:.4f}")
        print(f"IC均值: {ic_results['ic_mean']:.4f}")
        print(f"IC标准差: {ic_results['ic_std']:.4f}")
        print(f"IC_IR: {ic_results['ic_ir']:.4f}")
        
        # 绘制单因子检验结果图
        plt.figure(figsize=(15, 5))
        
        # IC时间序列图
        plt.subplot(1, 2, 1)
        plt.plot(ic_results['ic_series'])
        plt.title(f'{factor_name} IC时间序列')
        plt.xlabel('日期')
        plt.ylabel('IC值')
        
        # 分组收益对比图
        plt.subplot(1, 2, 2)
        group_results['mean'].plot(kind='bar')
        plt.title(f'{factor_name} 分组收益对比')
        plt.xlabel('分组')
        plt.ylabel('平均收益')
        
        plt.tight_layout()
        plt.show()
        
        return self.factor_test_results[factor_name]
    
    def _factor_group_backtest(self, test_data, factor_col):
        """因子分组回测"""
        results = []
        
        # 按日期分组
        for date, date_group in test_data.groupby('date'):
            if len(date_group) < 50:  # 至少需要50只股票
                continue
                
            # 按因子值分为5组
            date_group['factor_quantile'] = pd.qcut(
                date_group[factor_col], 
                q=5, 
                labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'],
                duplicates='drop'
            )
            
            # 计算各组平均收益率
            group_returns = date_group.groupby('factor_quantile')['return_1d'].mean()
            
            for quantile, ret in group_returns.items():
                results.append({
                    'date': date,
                    'quantile': quantile,
                    'return': ret
                })
        
        results_df = pd.DataFrame(results)
        
        # 计算各组累计收益
        if len(results_df) > 0:
            group_summary = results_df.groupby('quantile')['return'].agg(['mean', 'std', 'count'])
            group_summary['sharpe'] = group_summary['mean'] / group_summary['std']
            return group_summary
        else:
            return pd.DataFrame()
    
    def _calculate_ic(self, test_data, factor_col):
        """计算IC指标"""
        ic_values = []
        
        # 按日期计算IC
        for date, date_group in test_data.groupby('date'):
            if len(date_group) < 20:
                continue
                
            # 计算Pearson相关系数作为IC
            ic = date_group[factor_col].corr(date_group['return_1d'])
            if not np.isnan(ic):
                ic_values.append(ic)
        
        if len(ic_values) > 0:
            ic_mean = np.mean(ic_values)
            ic_std = np.std(ic_values)
            ic_ir = ic_mean / ic_std if ic_std > 0 else 0
            
            return {
                'ic_series': ic_values,
                'ic_mean': ic_mean,
                'ic_std': ic_std,
                'ic_ir': ic_ir
            }
        else:
            return {'ic_series': [], 'ic_mean': 0, 'ic_std': 0, 'ic_ir': 0}
    
    def build_market_neutral_portfolio(self, selected_factors):
        """
        构建市场中性组合
        
        Parameters:
        -----------
        selected_factors : list
            选择的有效因子列表
        """
        print(f"\n=== 构建市场中性组合 ===")
        print(f"使用因子: {selected_factors}")
        
        # 合成因子得分
        composite_scores = self._calculate_composite_score(selected_factors)
        
        if composite_scores is None:
            print("无法计算合成得分")
            return None
        
        # 选股：选择得分最高的股票做多，得分最低的股票做空
        portfolio_results = []
        
        for date, date_group in composite_scores.groupby('date'):
            if len(date_group) < 100:
                continue
            
            # 选择前20%做多，后20%做空
            n_stocks = len(date_group)
            n_long = int(n_stocks * 0.2)
            n_short = int(n_stocks * 0.2)
            
            # 按得分排序
            sorted_stocks = date_group.sort_values('composite_score', ascending=False)
            
            long_stocks = sorted_stocks.head(n_long)
            short_stocks = sorted_stocks.tail(n_short)
            
            # 计算组合收益
            long_return = long_stocks['return_1d'].mean()
            short_return = short_stocks['return_1d'].mean()
            portfolio_return = long_return - short_return  # 多空对冲
            
            portfolio_results.append({
                'date': date,
                'long_return': long_return,
                'short_return': short_return,
                'portfolio_return': portfolio_return,
                'n_long': len(long_stocks),
                'n_short': len(short_stocks)
            })
        
        portfolio_df = pd.DataFrame(portfolio_results)
        
        if len(portfolio_df) > 0:
            # 计算策略表现
            strategy_performance = self._analyze_strategy_performance(portfolio_df)
            return portfolio_df, strategy_performance
        else:
            print("无法构建有效组合")
            return None, None
    
    def _calculate_composite_score(self, selected_factors):
        """计算合成因子得分"""
        if not selected_factors:
            return None
        
        # 获取所有中性化后的因子数据
        all_neutralized_data = []
        
        for factor in selected_factors:
            neutralized_data = self.industry_neutralization(factor)
            if not neutralized_data.empty:
                all_neutralized_data.append(neutralized_data)
        
        if not all_neutralized_data:
            return None
        
        # 合并所有因子数据
        merged_data = all_neutralized_data[0]
        for i in range(1, len(all_neutralized_data)):
            merged_data = merged_data.merge(
                all_neutralized_data[i], 
                on=['date', 'ts_code'], 
                how='inner'
            )
        
        # 添加收益率数据
        merged_data = merged_data.merge(
            self.factor_data[['date', 'ts_code', 'return_1d']], 
            on=['date', 'ts_code'], 
            how='inner'
        )
        
        # 计算等权重合成得分
        factor_cols = [f'{factor}_neutralized' for factor in selected_factors]
        
        # 标准化各因子
        for col in factor_cols:
            merged_data[f'{col}_std'] = merged_data.groupby('date')[col].transform(
                lambda x: (x - x.mean()) / x.std() if x.std() > 0 else 0
            )
        
        # 计算合成得分
        std_cols = [f'{col}_std' for col in factor_cols]
        merged_data['composite_score'] = merged_data[std_cols].mean(axis=1)
        
        return merged_data
    
    def _analyze_strategy_performance(self, portfolio_df):
        """分析策略表现"""
        returns = portfolio_df['portfolio_return']
        
        # 计算关键指标
        total_return = (1 + returns).prod() - 1
        annual_return = (1 + returns.mean()) ** 252 - 1
        annual_vol = returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
        max_drawdown = self._calculate_max_drawdown(returns)
        win_rate = (returns > 0).mean()
        
        performance = {
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_vol,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate
        }
        
        print(f"\n=== 策略表现分析 ===")
        print(f"总收益率: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"年化波动率: {annual_vol:.2%}")
        print(f"夏普比率: {sharpe_ratio:.3f}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"胜率: {win_rate:.2%}")
        
        return performance
    
    def _calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def plot_results(self, portfolio_df):
        """绘制结果图表"""
        if portfolio_df is None or len(portfolio_df) == 0:
            print("没有数据可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 累计收益率
        cumulative_returns = (1 + portfolio_df['portfolio_return']).cumprod()
        axes[0, 0].plot(portfolio_df['date'], cumulative_returns)
        axes[0, 0].set_title('市场中性策略累计收益率')
        axes[0, 0].set_ylabel('累计收益率')
        
        # 收益率分布
        axes[0, 1].hist(portfolio_df['portfolio_return'], bins=30, alpha=0.7)
        axes[0, 1].set_title('日收益率分布')
        axes[0, 1].set_xlabel('日收益率')
        
        # 多空收益对比
        axes[1, 0].plot(portfolio_df['date'], portfolio_df['long_return'], label='多头收益', alpha=0.7)
        axes[1, 0].plot(portfolio_df['date'], portfolio_df['short_return'], label='空头收益', alpha=0.7)
        axes[1, 0].plot(portfolio_df['date'], portfolio_df['portfolio_return'], label='多空对冲', linewidth=2)
        axes[1, 0].set_title('多空收益对比')
        axes[1, 0].legend()
        
        # 回撤分析
        cumulative = (1 + portfolio_df['portfolio_return']).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        axes[1, 1].fill_between(portfolio_df['date'], drawdown, 0, alpha=0.3, color='red')
        axes[1, 1].set_title('策略回撤')
        axes[1, 1].set_ylabel('回撤')
        
        plt.tight_layout()
        plt.show()


def main():
    """主函数"""
    print("市场中性策略分析")
    print("="*50)
    
    # 创建策略分析器
    strategy = MarketNeutralStrategy()
    
    # 1. 加载数据
    if not strategy.load_data():
        print("数据加载失败，程序退出")
        return
    
    # 2. 选择要测试的因子
    test_factors = ['pe_ratio', 'pb_ratio', 'roe', 'momentum_20d']
    
    print(f"\n测试因子: {test_factors}")
    
    # 3. 对每个因子进行行业中性化和单因子检验
    valid_factors = []
    
    for factor in test_factors:
        print(f"\n{'='*30}")
        print(f"分析因子: {factor}")
        print(f"{'='*30}")
        
        # 行业中性化
        neutralized_data = strategy.industry_neutralization(factor)
        
        if not neutralized_data.empty:
            # 单因子检验
            test_result = strategy.single_factor_test(factor, neutralized_data)
            
            # 判断因子是否有效（IC_IR > 0.5作为筛选标准）
            if test_result and test_result['ic_results']['ic_ir'] > 0.5:
                valid_factors.append(factor)
                print(f"✓ {factor} 通过检验")
            else:
                print(f"✗ {factor} 未通过检验")
    
    print(f"\n有效因子: {valid_factors}")
    
    # 4. 构建市场中性组合
    if valid_factors:
        portfolio_df, performance = strategy.build_market_neutral_portfolio(valid_factors)
        
        if portfolio_df is not None:
            # 5. 绘制结果
            strategy.plot_results(portfolio_df)
        else:
            print("组合构建失败")
    else:
        print("没有找到有效因子，无法构建组合")
    
    print("\n分析完成！")


if __name__ == "__main__":
    main()
