"""
指数中性基础分析
简单展示指数中性和行业中性的核心概念

数据文件：
- CSI800stocks.csv: 中证800成分股权重
- swind.xlsx: 行业分类数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载数据"""
    print("=== 加载数据 ===")
    
    # 加载中证800权重数据
    weights_path = r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\CSI800stocks.csv"
    weights_df = pd.read_csv(weights_path)
    weights_df['trade_date'] = pd.to_datetime(weights_df['trade_date'])
    print(f"权重数据: {weights_df.shape[0]}条记录")
    
    # 加载行业数据
    industry_path = r"C:\Users\<USER>\Desktop\金元顺安\指数中性\data\swind.xlsx"
    industry_df = pd.read_excel(industry_path)
    # 取最新的行业分类
    industry_df = industry_df.sort_values('in_date').groupby('ts_code').last().reset_index()
    print(f"行业数据: {industry_df.shape[0]}只股票")
    
    return weights_df, industry_df

def analyze_index_composition(weights_df, industry_df):
    """分析指数构成"""
    print("\n=== 指数构成分析 ===")
    
    # 取最新一期的权重数据
    latest_date = weights_df['trade_date'].max()
    latest_weights = weights_df[weights_df['trade_date'] == latest_date].copy()
    
    print(f"分析日期: {latest_date.strftime('%Y-%m-%d')}")
    print(f"成分股数量: {len(latest_weights)}")
    print(f"权重总和: {latest_weights['weight'].sum():.2f}%")
    
    # 权重分布
    print(f"\n权重分布:")
    print(f"前10大权重股占比: {latest_weights.nlargest(10, 'weight')['weight'].sum():.2f}%")
    print(f"前50大权重股占比: {latest_weights.nlargest(50, 'weight')['weight'].sum():.2f}%")
    
    # 显示前10大权重股
    top10 = latest_weights.nlargest(10, 'weight')
    print(f"\n前10大权重股:")
    for i, row in top10.iterrows():
        print(f"{row['ts_code']}: {row['weight']:.2f}%")
    
    return latest_weights

def analyze_industry_distribution(latest_weights, industry_df):
    """分析行业分布"""
    print("\n=== 行业分布分析 ===")
    
    # 合并权重和行业数据
    merged_df = latest_weights.merge(industry_df[['ts_code', 'l1_name']], 
                                   on='ts_code', how='left')
    
    # 计算各行业权重
    industry_weights = merged_df.groupby('l1_name')['weight'].sum().sort_values(ascending=False)
    
    print("各行业权重分布:")
    for industry, weight in industry_weights.head(10).items():
        print(f"{industry}: {weight:.2f}%")
    
    # 可视化行业分布
    plt.figure(figsize=(12, 8))
    
    plt.subplot(2, 2, 1)
    industry_weights.head(10).plot(kind='bar')
    plt.title('前10大行业权重分布')
    plt.ylabel('权重 (%)')
    plt.xticks(rotation=45)
    
    plt.subplot(2, 2, 2)
    plt.pie(industry_weights.head(8).values, labels=industry_weights.head(8).index, autopct='%1.1f%%')
    plt.title('前8大行业权重占比')
    
    plt.tight_layout()
    plt.show()
    
    return merged_df, industry_weights

def demonstrate_index_neutral():
    """演示指数中性概念"""
    print("\n=== 指数中性概念演示 ===")
    
    # 假设我们选择5只股票做多
    long_stocks = ['601318.SH', '600519.SH', '300750.SZ', '600036.SH', '601166.SH']
    long_weights = [0.2, 0.2, 0.2, 0.2, 0.2]  # 等权重
    
    print("多头组合:")
    for stock, weight in zip(long_stocks, long_weights):
        print(f"{stock}: {weight:.1%}")
    
    print(f"多头总权重: {sum(long_weights):.1%}")
    
    print("\n指数中性的核心思想:")
    print("1. 构建多头组合（选股策略）")
    print("2. 按指数权重构建等值空头对冲")
    print("3. 消除市场系统性风险，获得纯Alpha收益")
    print("4. 组合对指数的净暴露 ≈ 0")
    
    return long_stocks, long_weights

def demonstrate_industry_neutral(merged_df, long_stocks):
    """演示行业中性概念"""
    print("\n=== 行业中性概念演示 ===")
    
    # 分析多头股票的行业分布
    long_industries = {}
    for stock in long_stocks:
        industry_info = merged_df[merged_df['ts_code'] == stock]
        if not industry_info.empty:
            industry = industry_info.iloc[0]['l1_name']
            long_industries[stock] = industry
            print(f"{stock}: {industry}")
    
    # 统计多头组合的行业暴露
    industry_exposure = {}
    for stock, industry in long_industries.items():
        if industry in industry_exposure:
            industry_exposure[industry] += 0.2  # 等权重
        else:
            industry_exposure[industry] = 0.2
    
    print(f"\n多头组合行业暴露:")
    for industry, exposure in industry_exposure.items():
        print(f"{industry}: {exposure:.1%}")
    
    print("\n行业中性的核心思想:")
    print("1. 在每个行业内部保持多空平衡")
    print("2. 消除行业配置偏差的影响")
    print("3. 获得纯粹的个股选择收益")
    print("4. 各行业净暴露 ≈ 0")
    
    return industry_exposure

def calculate_neutral_portfolio(latest_weights, long_stocks, method='index'):
    """计算中性组合权重"""
    print(f"\n=== {method}中性组合计算 ===")
    
    # 构建权重字典
    index_weights = latest_weights.set_index('ts_code')['weight'].to_dict()
    
    # 多头权重（等权重）
    long_weight_per_stock = 1.0 / len(long_stocks)
    portfolio_weights = {}
    
    # 多头部分
    for stock in long_stocks:
        portfolio_weights[stock] = long_weight_per_stock
    
    if method == 'index':
        # 指数中性：按指数权重做空
        total_long = sum(portfolio_weights.values())
        for stock, index_weight in index_weights.items():
            if stock in portfolio_weights:
                # 多头股票：净权重 = 多头权重 - 指数权重*总多头权重
                portfolio_weights[stock] = long_weight_per_stock - (index_weight/100) * total_long
            else:
                # 非多头股票：按指数权重做空
                portfolio_weights[stock] = -(index_weight/100) * total_long
    
    # 计算关键指标
    total_long = sum([w for w in portfolio_weights.values() if w > 0])
    total_short = abs(sum([w for w in portfolio_weights.values() if w < 0]))
    net_exposure = sum(portfolio_weights.values())
    
    print(f"多头总权重: {total_long:.3f}")
    print(f"空头总权重: {total_short:.3f}")
    print(f"净市场暴露: {net_exposure:.6f}")
    print(f"多空比例: {total_long/total_short:.3f}" if total_short > 0 else "无空头")
    
    # 显示主要持仓
    sorted_positions = sorted(portfolio_weights.items(), key=lambda x: abs(x[1]), reverse=True)
    
    print(f"\n主要多头持仓:")
    count = 0
    for stock, weight in sorted_positions:
        if weight > 0 and count < 5:
            print(f"{stock}: {weight:.4f}")
            count += 1
    
    print(f"\n主要空头持仓:")
    count = 0
    for stock, weight in sorted_positions:
        if weight < 0 and count < 5:
            print(f"{stock}: {weight:.4f}")
            count += 1
    
    return portfolio_weights

def create_summary_report(industry_weights, portfolio_weights):
    """生成总结报告"""
    print("\n" + "="*60)
    print("指数中性分析总结报告")
    print("="*60)
    
    print(f"\n1. 数据概况:")
    print(f"   - 中证800成分股: {len(portfolio_weights)}只")
    print(f"   - 行业分类: {len(industry_weights)}个一级行业")
    print(f"   - 前3大行业: {', '.join(industry_weights.head(3).index)}")
    
    print(f"\n2. 指数中性核心要点:")
    print(f"   - 目标: 消除市场系统性风险")
    print(f"   - 方法: 多头选股 + 指数权重空头对冲")
    print(f"   - 结果: 净市场暴露接近0")
    
    print(f"\n3. 行业中性核心要点:")
    print(f"   - 目标: 消除行业配置偏差")
    print(f"   - 方法: 各行业内部多空平衡")
    print(f"   - 结果: 各行业净暴露接近0")
    
    print(f"\n4. 实际应用价值:")
    print(f"   - 风险管理: 有效控制系统性风险")
    print(f"   - 收益来源: 专注于个股选择能力")
    print(f"   - 适用场景: 震荡市场、风险厌恶投资者")

def main():
    """主函数"""
    print("指数中性和行业中性基础分析")
    print("="*50)
    
    # 1. 加载数据
    weights_df, industry_df = load_data()
    
    # 2. 分析指数构成
    latest_weights = analyze_index_composition(weights_df, industry_df)
    
    # 3. 分析行业分布
    merged_df, industry_weights = analyze_industry_distribution(latest_weights, industry_df)
    
    # 4. 演示指数中性概念
    long_stocks, long_weights = demonstrate_index_neutral()
    
    # 5. 演示行业中性概念
    industry_exposure = demonstrate_industry_neutral(merged_df, long_stocks)
    
    # 6. 计算中性组合
    portfolio_weights = calculate_neutral_portfolio(latest_weights, long_stocks, method='index')
    
    # 7. 生成总结报告
    create_summary_report(industry_weights, portfolio_weights)
    
    print(f"\n分析完成！")

if __name__ == "__main__":
    main()
