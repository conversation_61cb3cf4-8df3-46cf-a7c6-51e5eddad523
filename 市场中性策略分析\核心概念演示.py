"""
市场中性策略核心概念演示
简化版本，重点展示关键思路
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def demonstrate_industry_neutralization():
    """演示行业中性化的核心思路"""
    print("=== 行业中性化演示 ===")
    
    # 模拟数据：3个行业，每个行业5只股票
    np.random.seed(42)
    data = []
    
    industries = ['银行', '科技', '医药']
    for industry in industries:
        for i in range(5):
            # 行业因素：银行PE普遍较低，科技较高，医药中等
            if industry == '银行':
                base_pe = 8 + np.random.normal(0, 1)
            elif industry == '科技':
                base_pe = 25 + np.random.normal(0, 3)
            else:  # 医药
                base_pe = 15 + np.random.normal(0, 2)
            
            data.append({
                'stock': f'{industry}_{i+1}',
                'industry': industry,
                'pe_ratio': max(base_pe, 1),  # PE不能为负
                'return': np.random.normal(0.01, 0.02)  # 随机收益率
            })
    
    df = pd.DataFrame(data)
    
    print("原始PE数据:")
    print(df.groupby('industry')['pe_ratio'].agg(['mean', 'std']).round(2))
    
    # 行业中性化：剔除行业平均水平
    df['industry_mean_pe'] = df.groupby('industry')['pe_ratio'].transform('mean')
    df['pe_neutralized'] = df['pe_ratio'] - df['industry_mean_pe']
    
    print("\n中性化后PE数据:")
    print(df.groupby('industry')['pe_neutralized'].agg(['mean', 'std']).round(4))
    
    print("\n中性化的作用：")
    print("- 原始PE：不同行业差异很大（银行8，科技25，医药15）")
    print("- 中性化后：各行业均值都接近0，消除了行业系统性差异")
    print("- 保留了：同行业内个股的相对差异")
    
    return df

def demonstrate_factor_testing():
    """演示单因子检验"""
    print("\n=== 单因子检验演示 ===")
    
    # 生成模拟数据：因子值和收益率
    np.random.seed(42)
    n_stocks = 100
    n_days = 50
    
    # 模拟一个有效因子：PE越低，未来收益越高
    results = []
    for day in range(n_days):
        for stock in range(n_stocks):
            pe = np.random.lognormal(2.5, 0.5)  # PE分布
            # 构造负相关关系：PE越低，收益越高
            return_signal = -0.002 * (pe - 15)  # 信号强度
            noise = np.random.normal(0, 0.02)   # 噪音
            actual_return = return_signal + noise
            
            results.append({
                'day': day,
                'stock': stock,
                'pe_factor': pe,
                'return': actual_return
            })
    
    df = pd.DataFrame(results)
    
    # 计算IC（信息系数）
    daily_ic = []
    for day in range(n_days):
        day_data = df[df['day'] == day]
        ic = day_data['pe_factor'].corr(day_data['return'])
        daily_ic.append(ic)
    
    ic_mean = np.mean(daily_ic)
    ic_std = np.std(daily_ic)
    ic_ir = ic_mean / ic_std if ic_std > 0 else 0
    
    print(f"IC统计:")
    print(f"  IC均值: {ic_mean:.4f}")
    print(f"  IC标准差: {ic_std:.4f}")
    print(f"  IC_IR: {ic_ir:.4f}")
    
    # 分组回测
    df['pe_quintile'] = df.groupby('day')['pe_factor'].transform(
        lambda x: pd.qcut(x, 5, labels=['Q1', 'Q2', 'Q3', 'Q4', 'Q5'], duplicates='drop')
    )
    
    group_returns = df.groupby('pe_quintile')['return'].mean()
    print(f"\n分组回测结果:")
    for quintile, ret in group_returns.items():
        print(f"  {quintile} (PE从低到高): {ret:.4f}")
    
    print(f"\n多空收益 (Q1-Q5): {group_returns['Q1'] - group_returns['Q5']:.4f}")
    
    return df, daily_ic

def demonstrate_market_neutral_portfolio():
    """演示市场中性组合构建"""
    print("\n=== 市场中性组合演示 ===")
    
    # 假设我们有一个有效因子，构建组合
    np.random.seed(42)
    stocks = [f'Stock_{i:03d}' for i in range(100)]
    
    # 模拟因子得分
    factor_scores = np.random.normal(0, 1, 100)
    
    # 按因子得分排序
    stock_scores = list(zip(stocks, factor_scores))
    stock_scores.sort(key=lambda x: x[1], reverse=True)
    
    # 选择前20只做多，后20只做空
    long_stocks = stock_scores[:20]
    short_stocks = stock_scores[-20:]
    
    print("多头组合 (因子得分最高):")
    for i, (stock, score) in enumerate(long_stocks[:5]):
        print(f"  {stock}: {score:.3f}")
    print("  ...")
    
    print("\n空头组合 (因子得分最低):")
    for i, (stock, score) in enumerate(short_stocks[:5]):
        print(f"  {stock}: {score:.3f}")
    print("  ...")
    
    # 模拟组合收益
    # 假设因子有效：高得分股票收益更高
    long_returns = []
    short_returns = []
    
    for _, score in long_stocks:
        # 高得分股票有更高的期望收益
        ret = 0.001 * score + np.random.normal(0, 0.02)
        long_returns.append(ret)
    
    for _, score in short_stocks:
        # 低得分股票有更低的期望收益
        ret = 0.001 * score + np.random.normal(0, 0.02)
        short_returns.append(ret)
    
    long_avg = np.mean(long_returns)
    short_avg = np.mean(short_returns)
    portfolio_return = long_avg - short_avg  # 多空对冲
    
    print(f"\n组合表现:")
    print(f"  多头平均收益: {long_avg:.4f}")
    print(f"  空头平均收益: {short_avg:.4f}")
    print(f"  多空对冲收益: {portfolio_return:.4f}")
    
    print(f"\n市场中性的优势:")
    print(f"  - 不依赖市场方向：无论牛市熊市都能盈利")
    print(f"  - 风险可控：多空对冲降低了组合波动")
    print(f"  - 纯Alpha收益：收益来源于选股能力而非市场Beta")

def create_visualization():
    """创建可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    # 模拟策略收益率时间序列
    np.random.seed(42)
    days = 252  # 一年交易日
    
    # 市场收益率（有趋势和波动）
    market_trend = np.linspace(0, 0.1, days)  # 年化10%趋势
    market_noise = np.random.normal(0, 0.015, days)  # 日波动1.5%
    market_returns = market_trend + market_noise
    market_cumulative = np.cumprod(1 + market_returns)
    
    # 市场中性策略收益率（无趋势，较小波动）
    neutral_returns = np.random.normal(0.0003, 0.008, days)  # 年化约8%，波动较小
    neutral_cumulative = np.cumprod(1 + neutral_returns)
    
    # 普通多头策略（市场+Alpha）
    alpha_returns = np.random.normal(0.0002, 0.005, days)
    long_only_returns = market_returns + alpha_returns
    long_only_cumulative = np.cumprod(1 + long_only_returns)
    
    # 绘图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 累计收益率对比
    axes[0, 0].plot(market_cumulative, label='市场指数', linewidth=2)
    axes[0, 0].plot(long_only_cumulative, label='普通多头策略', linewidth=2)
    axes[0, 0].plot(neutral_cumulative, label='市场中性策略', linewidth=2)
    axes[0, 0].set_title('策略收益率对比')
    axes[0, 0].set_ylabel('累计收益率')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 收益率分布
    axes[0, 1].hist(market_returns, bins=30, alpha=0.5, label='市场收益率', density=True)
    axes[0, 1].hist(neutral_returns, bins=30, alpha=0.5, label='中性策略', density=True)
    axes[0, 1].set_title('日收益率分布')
    axes[0, 1].set_xlabel('日收益率')
    axes[0, 1].legend()
    
    # 滚动波动率
    window = 20
    market_vol = pd.Series(market_returns).rolling(window).std() * np.sqrt(252)
    neutral_vol = pd.Series(neutral_returns).rolling(window).std() * np.sqrt(252)
    
    axes[1, 0].plot(market_vol, label='市场波动率', alpha=0.7)
    axes[1, 0].plot(neutral_vol, label='中性策略波动率', alpha=0.7)
    axes[1, 0].set_title('滚动年化波动率')
    axes[1, 0].set_ylabel('年化波动率')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 回撤分析
    market_dd = (market_cumulative / np.maximum.accumulate(market_cumulative) - 1)
    neutral_dd = (neutral_cumulative / np.maximum.accumulate(neutral_cumulative) - 1)
    
    axes[1, 1].fill_between(range(len(market_dd)), market_dd, 0, alpha=0.3, label='市场回撤')
    axes[1, 1].fill_between(range(len(neutral_dd)), neutral_dd, 0, alpha=0.3, label='中性策略回撤')
    axes[1, 1].set_title('策略回撤对比')
    axes[1, 1].set_ylabel('回撤')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 计算关键指标
    print("策略表现对比:")
    print(f"  市场指数年化收益: {(market_cumulative[-1] ** (252/days) - 1):.2%}")
    print(f"  市场指数年化波动: {np.std(market_returns) * np.sqrt(252):.2%}")
    print(f"  市场指数最大回撤: {np.min(market_dd):.2%}")
    
    print(f"\n  中性策略年化收益: {(neutral_cumulative[-1] ** (252/days) - 1):.2%}")
    print(f"  中性策略年化波动: {np.std(neutral_returns) * np.sqrt(252):.2%}")
    print(f"  中性策略最大回撤: {np.min(neutral_dd):.2%}")

def main():
    """主函数"""
    print("市场中性策略核心概念演示")
    print("="*50)
    
    # 1. 行业中性化演示
    df_industry = demonstrate_industry_neutralization()
    
    # 2. 单因子检验演示
    df_factor, ic_series = demonstrate_factor_testing()
    
    # 3. 市场中性组合演示
    demonstrate_market_neutral_portfolio()
    
    # 4. 可视化展示
    create_visualization()
    
    print("\n" + "="*50)
    print("核心要点总结:")
    print("1. 行业中性化：剔除行业系统性因素，保留个股相对优势")
    print("2. 单因子检验：通过IC分析验证因子预测能力")
    print("3. 市场中性：多空对冲消除市场风险，获取纯Alpha收益")
    print("4. 风险控制：通过中性化处理有效控制各类风险暴露")
    print("="*50)

if __name__ == "__main__":
    main()
